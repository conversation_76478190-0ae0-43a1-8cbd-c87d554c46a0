import React from 'react'
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import './FormStyles.css';
import vid from '../images/backgroundvideo.mp4'

function Signup() {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    password: "",
    role: "User",
  });
  const { signup } = useAuth();
  const navigate = useNavigate();

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    console.log('Signup form data:', formData);

    // Validate form data
    if (!formData.name || !formData.email || !formData.password) {
      alert('Please fill in all fields');
      return;
    }

    const result = await signup(formData);
    console.log('Signup result:', result);

    if (result.success) {
      alert("Signup successful! Please login with your credentials.");
      // Redirect to login page
      navigate('/login');
    } else {
      alert(`Signup failed: ${result.message}`);
    }
  };
  window.addEventListener('load', function() {
    const video = document.getElementById('myVideo');
    video.play();
  });
  return (
    <div>
       <div>
                  <section class="showcase">
                      <video id="myVideo" loop muted>
                        <source src={vid} type="video/mp4"/>
                      </video>
                      <h1 id="head">SIGNUP</h1>
                    </section>
      
                  </div>
      <div>
        <p>




        </p>
      </div>
      <form onSubmit={handleSubmit}>
      <input
        type="text"
        name="name"
        placeholder="Name"
        value={formData.name}
        onChange={handleChange}
        required
      />
      <input
        type="email"
        name="email"
        placeholder="Email"
        value={formData.email}
        onChange={handleChange}
        required
      />
      <input
        type="password"
        name="password"
        placeholder="Password"
        value={formData.password}
        onChange={handleChange}
        required
      />
      <select name="role" value={formData.role} onChange={handleChange} required>
        <option value="User">User</option>
        <option value="Doctor">Doctor</option>
      </select>
      <button type="submit">Sign Up</button>
    </form>
    </div>
    
  );
}

export default Signup;
