{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\my-app\\\\my-app\\\\src\\\\context\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Check if user is authenticated on app load\n  useEffect(() => {\n    checkAuthStatus();\n  }, []);\n  const checkAuthStatus = async () => {\n    try {\n      const response = await axios.get('http://localhost:5000/api/auth/verify', {\n        withCredentials: true\n      });\n      setUser(response.data.user);\n    } catch (error) {\n      console.log('User not authenticated');\n      setUser(null);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const login = async (email, password) => {\n    try {\n      const response = await axios.post('http://localhost:5000/api/auth/login', {\n        email,\n        password\n      }, {\n        withCredentials: true\n      });\n\n      // Set user directly from login response\n      setUser(response.data.user);\n      return {\n        success: true,\n        message: response.data.message,\n        user: response.data.user\n      };\n    } catch (error) {\n      var _error$response, _error$response$data;\n      return {\n        success: false,\n        message: ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Login failed'\n      };\n    }\n  };\n  const signup = async userData => {\n    try {\n      console.log('Sending signup request with data:', userData);\n      const response = await axios.post('http://localhost:5000/api/auth/signup', userData, {\n        withCredentials: true\n      });\n      console.log('Signup response:', response.data);\n      return {\n        success: true,\n        message: response.data.message\n      };\n    } catch (error) {\n      var _error$response2, _error$response3, _error$response3$data;\n      console.error('Signup error:', error);\n      console.error('Error response:', (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.data);\n      return {\n        success: false,\n        message: ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.message) || error.message || 'Signup failed'\n      };\n    }\n  };\n  const logout = async () => {\n    try {\n      await axios.post('http://localhost:5000/api/auth/logout', {}, {\n        withCredentials: true\n      });\n      setUser(null);\n      // Clear any stored authentication data\n      localStorage.removeItem('user');\n      sessionStorage.removeItem('user');\n      return {\n        success: true,\n        message: 'Logged out successfully'\n      };\n    } catch (error) {\n      // Even if logout request fails, clear local state\n      setUser(null);\n      localStorage.removeItem('user');\n      sessionStorage.removeItem('user');\n      return {\n        success: true,\n        // Return success to ensure UI updates\n        message: 'Logged out successfully'\n      };\n    }\n  };\n  const value = {\n    user,\n    login,\n    signup,\n    logout,\n    loading,\n    checkAuthStatus\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 111,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"NiO5z6JIqzX62LS5UWDgIqbZYyY=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "axios", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "loading", "setLoading", "checkAuthStatus", "response", "get", "withCredentials", "data", "error", "console", "log", "login", "email", "password", "post", "success", "message", "_error$response", "_error$response$data", "signup", "userData", "_error$response2", "_error$response3", "_error$response3$data", "logout", "localStorage", "removeItem", "sessionStorage", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/my-app/my-app/src/context/AuthContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\nimport axios from 'axios';\n\nconst AuthContext = createContext();\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Check if user is authenticated on app load\n  useEffect(() => {\n    checkAuthStatus();\n  }, []);\n\n  const checkAuthStatus = async () => {\n    try {\n      const response = await axios.get('http://localhost:5000/api/auth/verify', {\n        withCredentials: true\n      });\n      setUser(response.data.user);\n    } catch (error) {\n      console.log('User not authenticated');\n      setUser(null);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const login = async (email, password) => {\n    try {\n      const response = await axios.post('http://localhost:5000/api/auth/login', {\n        email,\n        password\n      }, {\n        withCredentials: true\n      });\n\n      // Set user directly from login response\n      setUser(response.data.user);\n      return {\n        success: true,\n        message: response.data.message,\n        user: response.data.user\n      };\n    } catch (error) {\n      return {\n        success: false,\n        message: error.response?.data?.message || 'Login failed'\n      };\n    }\n  };\n\n  const signup = async (userData) => {\n    try {\n      console.log('Sending signup request with data:', userData);\n      const response = await axios.post('http://localhost:5000/api/auth/signup', userData, {\n        withCredentials: true\n      });\n      console.log('Signup response:', response.data);\n      return { success: true, message: response.data.message };\n    } catch (error) {\n      console.error('Signup error:', error);\n      console.error('Error response:', error.response?.data);\n      return {\n        success: false,\n        message: error.response?.data?.message || error.message || 'Signup failed'\n      };\n    }\n  };\n\n  const logout = async () => {\n    try {\n      await axios.post('http://localhost:5000/api/auth/logout', {}, {\n        withCredentials: true\n      });\n      setUser(null);\n      // Clear any stored authentication data\n      localStorage.removeItem('user');\n      sessionStorage.removeItem('user');\n      return { success: true, message: 'Logged out successfully' };\n    } catch (error) {\n      // Even if logout request fails, clear local state\n      setUser(null);\n      localStorage.removeItem('user');\n      sessionStorage.removeItem('user');\n      return {\n        success: true, // Return success to ensure UI updates\n        message: 'Logged out successfully'\n      };\n    }\n  };\n\n  const value = {\n    user,\n    login,\n    signup,\n    logout,\n    loading,\n    checkAuthStatus\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,WAAW,gBAAGP,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMQ,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGT,UAAU,CAACM,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACAC,SAAS,CAAC,MAAM;IACdgB,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMhB,KAAK,CAACiB,GAAG,CAAC,uCAAuC,EAAE;QACxEC,eAAe,EAAE;MACnB,CAAC,CAAC;MACFN,OAAO,CAACI,QAAQ,CAACG,IAAI,CAACR,IAAI,CAAC;IAC7B,CAAC,CAAC,OAAOS,KAAK,EAAE;MACdC,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;MACrCV,OAAO,CAAC,IAAI,CAAC;IACf,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMS,KAAK,GAAG,MAAAA,CAAOC,KAAK,EAAEC,QAAQ,KAAK;IACvC,IAAI;MACF,MAAMT,QAAQ,GAAG,MAAMhB,KAAK,CAAC0B,IAAI,CAAC,sCAAsC,EAAE;QACxEF,KAAK;QACLC;MACF,CAAC,EAAE;QACDP,eAAe,EAAE;MACnB,CAAC,CAAC;;MAEF;MACAN,OAAO,CAACI,QAAQ,CAACG,IAAI,CAACR,IAAI,CAAC;MAC3B,OAAO;QACLgB,OAAO,EAAE,IAAI;QACbC,OAAO,EAAEZ,QAAQ,CAACG,IAAI,CAACS,OAAO;QAC9BjB,IAAI,EAAEK,QAAQ,CAACG,IAAI,CAACR;MACtB,CAAC;IACH,CAAC,CAAC,OAAOS,KAAK,EAAE;MAAA,IAAAS,eAAA,EAAAC,oBAAA;MACd,OAAO;QACLH,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE,EAAAC,eAAA,GAAAT,KAAK,CAACJ,QAAQ,cAAAa,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBV,IAAI,cAAAW,oBAAA,uBAApBA,oBAAA,CAAsBF,OAAO,KAAI;MAC5C,CAAC;IACH;EACF,CAAC;EAED,MAAMG,MAAM,GAAG,MAAOC,QAAQ,IAAK;IACjC,IAAI;MACFX,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEU,QAAQ,CAAC;MAC1D,MAAMhB,QAAQ,GAAG,MAAMhB,KAAK,CAAC0B,IAAI,CAAC,uCAAuC,EAAEM,QAAQ,EAAE;QACnFd,eAAe,EAAE;MACnB,CAAC,CAAC;MACFG,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEN,QAAQ,CAACG,IAAI,CAAC;MAC9C,OAAO;QAAEQ,OAAO,EAAE,IAAI;QAAEC,OAAO,EAAEZ,QAAQ,CAACG,IAAI,CAACS;MAAQ,CAAC;IAC1D,CAAC,CAAC,OAAOR,KAAK,EAAE;MAAA,IAAAa,gBAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACdd,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrCC,OAAO,CAACD,KAAK,CAAC,iBAAiB,GAAAa,gBAAA,GAAEb,KAAK,CAACJ,QAAQ,cAAAiB,gBAAA,uBAAdA,gBAAA,CAAgBd,IAAI,CAAC;MACtD,OAAO;QACLQ,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE,EAAAM,gBAAA,GAAAd,KAAK,CAACJ,QAAQ,cAAAkB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBf,IAAI,cAAAgB,qBAAA,uBAApBA,qBAAA,CAAsBP,OAAO,KAAIR,KAAK,CAACQ,OAAO,IAAI;MAC7D,CAAC;IACH;EACF,CAAC;EAED,MAAMQ,MAAM,GAAG,MAAAA,CAAA,KAAY;IACzB,IAAI;MACF,MAAMpC,KAAK,CAAC0B,IAAI,CAAC,uCAAuC,EAAE,CAAC,CAAC,EAAE;QAC5DR,eAAe,EAAE;MACnB,CAAC,CAAC;MACFN,OAAO,CAAC,IAAI,CAAC;MACb;MACAyB,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;MAC/BC,cAAc,CAACD,UAAU,CAAC,MAAM,CAAC;MACjC,OAAO;QAAEX,OAAO,EAAE,IAAI;QAAEC,OAAO,EAAE;MAA0B,CAAC;IAC9D,CAAC,CAAC,OAAOR,KAAK,EAAE;MACd;MACAR,OAAO,CAAC,IAAI,CAAC;MACbyB,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;MAC/BC,cAAc,CAACD,UAAU,CAAC,MAAM,CAAC;MACjC,OAAO;QACLX,OAAO,EAAE,IAAI;QAAE;QACfC,OAAO,EAAE;MACX,CAAC;IACH;EACF,CAAC;EAED,MAAMY,KAAK,GAAG;IACZ7B,IAAI;IACJY,KAAK;IACLQ,MAAM;IACNK,MAAM;IACNvB,OAAO;IACPE;EACF,CAAC;EAED,oBACEb,OAAA,CAACC,WAAW,CAACsC,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAA/B,QAAA,EAChCA;EAAQ;IAAAiC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAACnC,GAAA,CArGWF,YAAY;AAAAsC,EAAA,GAAZtC,YAAY;AAAA,IAAAsC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}