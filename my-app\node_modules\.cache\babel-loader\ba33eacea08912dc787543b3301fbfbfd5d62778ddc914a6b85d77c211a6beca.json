{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\my-app\\\\my-app\\\\src\\\\pages\\\\UserAppointmentHistory.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { useAuth } from '../context/AuthContext';\nimport './AppointmentsTable.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UserAppointmentHistory = () => {\n  _s();\n  const [appointments, setAppointments] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [selectedReport, setSelectedReport] = useState(null);\n  const {\n    user\n  } = useAuth();\n  useEffect(() => {\n    if (user) {\n      fetchUserAppointments();\n    }\n  }, [user]);\n  const fetchUserAppointments = async () => {\n    try {\n      const response = await axios.get(`http://localhost:5000/api/appointments/user/${user.email}`, {\n        withCredentials: true\n      });\n      setAppointments(response.data.appointments);\n    } catch (error) {\n      console.error('Error fetching user appointments:', error);\n      alert('Error fetching your appointments');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const viewReport = async appointmentId => {\n    try {\n      console.log(`📄 Fetching report for appointment: ${appointmentId}`);\n      const response = await axios.get(`http://localhost:5000/api/patient-reports/appointment/${appointmentId}`);\n      console.log(`✅ Report fetched successfully:`, response.data.report);\n      setSelectedReport(response.data.report);\n    } catch (error) {\n      var _error$response, _error$response2;\n      console.error('❌ Error fetching patient report:', error);\n      if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 404) {\n        alert('No patient report available for this appointment yet.');\n      } else if (((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.status) === 401) {\n        alert('Authentication required. Please log in again.');\n      } else {\n        var _error$response3, _error$response3$data;\n        alert('Error fetching patient report: ' + (((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.message) || error.message));\n      }\n    }\n  };\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString();\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case 'pending':\n        return '#BB86FC';\n      case 'completed':\n        return '#4CAF50';\n      case 'cancelled':\n        return '#f44336';\n      case 'rescheduled':\n        return '#FF9800';\n      default:\n        return '#888';\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        color: 'white',\n        textAlign: 'center',\n        padding: '2rem'\n      },\n      children: \"Loading your appointments...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '2rem',\n      color: 'white'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      style: {\n        textAlign: 'center',\n        marginBottom: '2rem'\n      },\n      children: \"My Appointment History\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 7\n    }, this), appointments.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n      style: {\n        textAlign: 'center'\n      },\n      children: \"You have no appointments yet.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        overflowX: 'auto'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"table\", {\n        className: \"appotable\",\n        children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n          children: /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Appointment Date\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Time Slot\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 81,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Doctor\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 82,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Department\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 84,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Visited\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Actions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n          children: appointments.map(appointment => /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"td\", {\n              children: formatDate(appointment.appointmentDate)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              children: appointment.timeSlot\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              children: appointment.doctor ? `Dr. ${appointment.doctor.firstName} ${appointment.doctor.lastName}` : 'N/A'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              children: appointment.department ? appointment.department.name : 'N/A'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: getStatusColor(appointment.status),\n                  fontWeight: 'bold',\n                  textTransform: 'capitalize'\n                },\n                children: appointment.status\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 21\n              }, this), appointment.rescheduled && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '0.8rem',\n                  color: '#FF9800'\n                },\n                children: \"Rescheduled\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 115,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: appointment.visited ? '#4CAF50' : '#f44336'\n                },\n                children: appointment.visited ? 'Yes' : 'No'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 121,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => viewReport(appointment._id),\n                style: {\n                  backgroundColor: '#007BFF',\n                  color: 'white',\n                  border: 'none',\n                  padding: '0.5rem 1rem',\n                  borderRadius: '4px',\n                  cursor: 'pointer'\n                },\n                children: \"View Report\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 19\n            }, this)]\n          }, appointment._id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 9\n    }, this), selectedReport && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        backgroundColor: 'rgba(0,0,0,0.8)',\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        zIndex: 1000\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: '#1e1e1e',\n          padding: '2rem',\n          borderRadius: '8px',\n          maxWidth: '600px',\n          maxHeight: '80vh',\n          overflow: 'auto',\n          color: 'white'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          style: {\n            color: '#BB86FC',\n            marginBottom: '1rem'\n          },\n          children: \"Patient Report\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Status:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 15\n          }, this), \" \", selectedReport.status]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Disease Details:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 15\n          }, this), \" \", selectedReport.diseaseDetails]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 13\n        }, this), selectedReport.importantMeasures && selectedReport.importantMeasures.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Important Measures:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            children: selectedReport.importantMeasures.map((measure, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n              children: [measure.label, \": \", measure.value]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 15\n        }, this), selectedReport.allergies && selectedReport.allergies.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Allergies:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 17\n          }, this), \" \", selectedReport.allergies.join(', ')]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 15\n        }, this), selectedReport.medications && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Medications:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 17\n          }, this), \" \", selectedReport.medications]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 15\n        }, this), selectedReport.followUpPlan && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Follow-up Plan:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 17\n          }, this), \" \", selectedReport.followUpPlan]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 15\n        }, this), selectedReport.labTests && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Lab Tests:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 17\n          }, this), \" \", selectedReport.labTests]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 15\n        }, this), selectedReport.dietaryAdvice && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Dietary Advice:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 17\n          }, this), \" \", selectedReport.dietaryAdvice]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 15\n        }, this), selectedReport.notes && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Doctor's Notes:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 17\n          }, this), \" \", selectedReport.notes]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setSelectedReport(null),\n          style: {\n            backgroundColor: '#dc3545',\n            color: 'white',\n            border: 'none',\n            padding: '0.5rem 1rem',\n            borderRadius: '4px',\n            cursor: 'pointer',\n            marginTop: '1rem'\n          },\n          children: \"Close\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 70,\n    columnNumber: 5\n  }, this);\n};\n_s(UserAppointmentHistory, \"hGyUvQTAJDM4c7iPo7V5jPL1UYs=\", false, function () {\n  return [useAuth];\n});\n_c = UserAppointmentHistory;\nexport default UserAppointmentHistory;\nvar _c;\n$RefreshReg$(_c, \"UserAppointmentHistory\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "useAuth", "jsxDEV", "_jsxDEV", "UserAppointmentHistory", "_s", "appointments", "setAppointments", "loading", "setLoading", "selectedReport", "setSelectedReport", "user", "fetchUserAppointments", "response", "get", "email", "withCredentials", "data", "error", "console", "alert", "viewReport", "appointmentId", "log", "report", "_error$response", "_error$response2", "status", "_error$response3", "_error$response3$data", "message", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "getStatusColor", "style", "color", "textAlign", "padding", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginBottom", "length", "overflowX", "className", "map", "appointment", "appointmentDate", "timeSlot", "doctor", "firstName", "lastName", "department", "name", "fontWeight", "textTransform", "rescheduled", "fontSize", "visited", "onClick", "_id", "backgroundColor", "border", "borderRadius", "cursor", "position", "top", "left", "right", "bottom", "display", "justifyContent", "alignItems", "zIndex", "max<PERSON><PERSON><PERSON>", "maxHeight", "overflow", "diseaseDetails", "importantMeasures", "measure", "index", "label", "value", "allergies", "join", "medications", "followUpPlan", "labTests", "dietaryAdvice", "notes", "marginTop", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/my-app/my-app/src/pages/UserAppointmentHistory.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { useAuth } from '../context/AuthContext';\nimport './AppointmentsTable.css';\n\nconst UserAppointmentHistory = () => {\n  const [appointments, setAppointments] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [selectedReport, setSelectedReport] = useState(null);\n  const { user } = useAuth();\n\n  useEffect(() => {\n    if (user) {\n      fetchUserAppointments();\n    }\n  }, [user]);\n\n  const fetchUserAppointments = async () => {\n    try {\n      const response = await axios.get(`http://localhost:5000/api/appointments/user/${user.email}`, {\n        withCredentials: true\n      });\n      setAppointments(response.data.appointments);\n    } catch (error) {\n      console.error('Error fetching user appointments:', error);\n      alert('Error fetching your appointments');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const viewReport = async (appointmentId) => {\n    try {\n      console.log(`📄 Fetching report for appointment: ${appointmentId}`);\n      const response = await axios.get(`http://localhost:5000/api/patient-reports/appointment/${appointmentId}`);\n      console.log(`✅ Report fetched successfully:`, response.data.report);\n      setSelectedReport(response.data.report);\n    } catch (error) {\n      console.error('❌ Error fetching patient report:', error);\n      if (error.response?.status === 404) {\n        alert('No patient report available for this appointment yet.');\n      } else if (error.response?.status === 401) {\n        alert('Authentication required. Please log in again.');\n      } else {\n        alert('Error fetching patient report: ' + (error.response?.data?.message || error.message));\n      }\n    }\n  };\n\n  const formatDate = (dateString) => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString();\n  };\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'pending': return '#BB86FC';\n      case 'completed': return '#4CAF50';\n      case 'cancelled': return '#f44336';\n      case 'rescheduled': return '#FF9800';\n      default: return '#888';\n    }\n  };\n\n  if (loading) {\n    return <div style={{ color: 'white', textAlign: 'center', padding: '2rem' }}>Loading your appointments...</div>;\n  }\n\n  return (\n    <div style={{ padding: '2rem', color: 'white' }}>\n      <h1 style={{ textAlign: 'center', marginBottom: '2rem' }}>My Appointment History</h1>\n      \n      {appointments.length === 0 ? (\n        <p style={{ textAlign: 'center' }}>You have no appointments yet.</p>\n      ) : (\n        <div style={{ overflowX: 'auto' }}>\n          <table className='appotable'>\n            <thead>\n              <tr>\n                <th>Appointment Date</th>\n                <th>Time Slot</th>\n                <th>Doctor</th>\n                <th>Department</th>\n                <th>Status</th>\n                <th>Visited</th>\n                <th>Actions</th>\n              </tr>\n            </thead>\n            <tbody>\n              {appointments.map((appointment) => (\n                <tr key={appointment._id}>\n                  <td>{formatDate(appointment.appointmentDate)}</td>\n                  <td>{appointment.timeSlot}</td>\n                  <td>\n                    {appointment.doctor ? \n                      `Dr. ${appointment.doctor.firstName} ${appointment.doctor.lastName}` : \n                      'N/A'\n                    }\n                  </td>\n                  <td>\n                    {appointment.department ? \n                      appointment.department.name : \n                      'N/A'\n                    }\n                  </td>\n                  <td>\n                    <span style={{ \n                      color: getStatusColor(appointment.status),\n                      fontWeight: 'bold',\n                      textTransform: 'capitalize'\n                    }}>\n                      {appointment.status}\n                    </span>\n                    {appointment.rescheduled && (\n                      <div style={{ fontSize: '0.8rem', color: '#FF9800' }}>\n                        Rescheduled\n                      </div>\n                    )}\n                  </td>\n                  <td>\n                    <span style={{ color: appointment.visited ? '#4CAF50' : '#f44336' }}>\n                      {appointment.visited ? 'Yes' : 'No'}\n                    </span>\n                  </td>\n                  <td>\n                    <button \n                      onClick={() => viewReport(appointment._id)}\n                      style={{\n                        backgroundColor: '#007BFF',\n                        color: 'white',\n                        border: 'none',\n                        padding: '0.5rem 1rem',\n                        borderRadius: '4px',\n                        cursor: 'pointer'\n                      }}\n                    >\n                      View Report\n                    </button>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      )}\n\n      {/* Patient Report Modal */}\n      {selectedReport && (\n        <div style={{\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          backgroundColor: 'rgba(0,0,0,0.8)',\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n          zIndex: 1000\n        }}>\n          <div style={{\n            backgroundColor: '#1e1e1e',\n            padding: '2rem',\n            borderRadius: '8px',\n            maxWidth: '600px',\n            maxHeight: '80vh',\n            overflow: 'auto',\n            color: 'white'\n          }}>\n            <h2 style={{ color: '#BB86FC', marginBottom: '1rem' }}>Patient Report</h2>\n            \n            <div style={{ marginBottom: '1rem' }}>\n              <strong>Status:</strong> {selectedReport.status}\n            </div>\n            \n            <div style={{ marginBottom: '1rem' }}>\n              <strong>Disease Details:</strong> {selectedReport.diseaseDetails}\n            </div>\n            \n            {selectedReport.importantMeasures && selectedReport.importantMeasures.length > 0 && (\n              <div style={{ marginBottom: '1rem' }}>\n                <strong>Important Measures:</strong>\n                <ul>\n                  {selectedReport.importantMeasures.map((measure, index) => (\n                    <li key={index}>{measure.label}: {measure.value}</li>\n                  ))}\n                </ul>\n              </div>\n            )}\n            \n            {selectedReport.allergies && selectedReport.allergies.length > 0 && (\n              <div style={{ marginBottom: '1rem' }}>\n                <strong>Allergies:</strong> {selectedReport.allergies.join(', ')}\n              </div>\n            )}\n            \n            {selectedReport.medications && (\n              <div style={{ marginBottom: '1rem' }}>\n                <strong>Medications:</strong> {selectedReport.medications}\n              </div>\n            )}\n            \n            {selectedReport.followUpPlan && (\n              <div style={{ marginBottom: '1rem' }}>\n                <strong>Follow-up Plan:</strong> {selectedReport.followUpPlan}\n              </div>\n            )}\n            \n            {selectedReport.labTests && (\n              <div style={{ marginBottom: '1rem' }}>\n                <strong>Lab Tests:</strong> {selectedReport.labTests}\n              </div>\n            )}\n            \n            {selectedReport.dietaryAdvice && (\n              <div style={{ marginBottom: '1rem' }}>\n                <strong>Dietary Advice:</strong> {selectedReport.dietaryAdvice}\n              </div>\n            )}\n            \n            {selectedReport.notes && (\n              <div style={{ marginBottom: '1rem' }}>\n                <strong>Doctor's Notes:</strong> {selectedReport.notes}\n              </div>\n            )}\n            \n            <button \n              onClick={() => setSelectedReport(null)}\n              style={{\n                backgroundColor: '#dc3545',\n                color: 'white',\n                border: 'none',\n                padding: '0.5rem 1rem',\n                borderRadius: '4px',\n                cursor: 'pointer',\n                marginTop: '1rem'\n              }}\n            >\n              Close\n            </button>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default UserAppointmentHistory;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAO,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjC,MAAMC,sBAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACU,OAAO,EAAEC,UAAU,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACY,cAAc,EAAEC,iBAAiB,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM;IAAEc;EAAK,CAAC,GAAGX,OAAO,CAAC,CAAC;EAE1BF,SAAS,CAAC,MAAM;IACd,IAAIa,IAAI,EAAE;MACRC,qBAAqB,CAAC,CAAC;IACzB;EACF,CAAC,EAAE,CAACD,IAAI,CAAC,CAAC;EAEV,MAAMC,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMd,KAAK,CAACe,GAAG,CAAC,+CAA+CH,IAAI,CAACI,KAAK,EAAE,EAAE;QAC5FC,eAAe,EAAE;MACnB,CAAC,CAAC;MACFV,eAAe,CAACO,QAAQ,CAACI,IAAI,CAACZ,YAAY,CAAC;IAC7C,CAAC,CAAC,OAAOa,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzDE,KAAK,CAAC,kCAAkC,CAAC;IAC3C,CAAC,SAAS;MACRZ,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMa,UAAU,GAAG,MAAOC,aAAa,IAAK;IAC1C,IAAI;MACFH,OAAO,CAACI,GAAG,CAAC,uCAAuCD,aAAa,EAAE,CAAC;MACnE,MAAMT,QAAQ,GAAG,MAAMd,KAAK,CAACe,GAAG,CAAC,yDAAyDQ,aAAa,EAAE,CAAC;MAC1GH,OAAO,CAACI,GAAG,CAAC,gCAAgC,EAAEV,QAAQ,CAACI,IAAI,CAACO,MAAM,CAAC;MACnEd,iBAAiB,CAACG,QAAQ,CAACI,IAAI,CAACO,MAAM,CAAC;IACzC,CAAC,CAAC,OAAON,KAAK,EAAE;MAAA,IAAAO,eAAA,EAAAC,gBAAA;MACdP,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,IAAI,EAAAO,eAAA,GAAAP,KAAK,CAACL,QAAQ,cAAAY,eAAA,uBAAdA,eAAA,CAAgBE,MAAM,MAAK,GAAG,EAAE;QAClCP,KAAK,CAAC,uDAAuD,CAAC;MAChE,CAAC,MAAM,IAAI,EAAAM,gBAAA,GAAAR,KAAK,CAACL,QAAQ,cAAAa,gBAAA,uBAAdA,gBAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;QACzCP,KAAK,CAAC,+CAA+C,CAAC;MACxD,CAAC,MAAM;QAAA,IAAAQ,gBAAA,EAAAC,qBAAA;QACLT,KAAK,CAAC,iCAAiC,IAAI,EAAAQ,gBAAA,GAAAV,KAAK,CAACL,QAAQ,cAAAe,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBX,IAAI,cAAAY,qBAAA,uBAApBA,qBAAA,CAAsBC,OAAO,KAAIZ,KAAK,CAACY,OAAO,CAAC,CAAC;MAC7F;IACF;EACF,CAAC;EAED,MAAMC,UAAU,GAAIC,UAAU,IAAK;IACjC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,CAAC;EAClC,CAAC;EAED,MAAMC,cAAc,GAAIT,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC,KAAK,WAAW;QAAE,OAAO,SAAS;MAClC,KAAK,WAAW;QAAE,OAAO,SAAS;MAClC,KAAK,aAAa;QAAE,OAAO,SAAS;MACpC;QAAS,OAAO,MAAM;IACxB;EACF,CAAC;EAED,IAAIpB,OAAO,EAAE;IACX,oBAAOL,OAAA;MAAKmC,KAAK,EAAE;QAAEC,KAAK,EAAE,OAAO;QAAEC,SAAS,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAO,CAAE;MAAAC,QAAA,EAAC;IAA4B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EACjH;EAEA,oBACE3C,OAAA;IAAKmC,KAAK,EAAE;MAAEG,OAAO,EAAE,MAAM;MAAEF,KAAK,EAAE;IAAQ,CAAE;IAAAG,QAAA,gBAC9CvC,OAAA;MAAImC,KAAK,EAAE;QAAEE,SAAS,EAAE,QAAQ;QAAEO,YAAY,EAAE;MAAO,CAAE;MAAAL,QAAA,EAAC;IAAsB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EAEpFxC,YAAY,CAAC0C,MAAM,KAAK,CAAC,gBACxB7C,OAAA;MAAGmC,KAAK,EAAE;QAAEE,SAAS,EAAE;MAAS,CAAE;MAAAE,QAAA,EAAC;IAA6B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,gBAEpE3C,OAAA;MAAKmC,KAAK,EAAE;QAAEW,SAAS,EAAE;MAAO,CAAE;MAAAP,QAAA,eAChCvC,OAAA;QAAO+C,SAAS,EAAC,WAAW;QAAAR,QAAA,gBAC1BvC,OAAA;UAAAuC,QAAA,eACEvC,OAAA;YAAAuC,QAAA,gBACEvC,OAAA;cAAAuC,QAAA,EAAI;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzB3C,OAAA;cAAAuC,QAAA,EAAI;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClB3C,OAAA;cAAAuC,QAAA,EAAI;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACf3C,OAAA;cAAAuC,QAAA,EAAI;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnB3C,OAAA;cAAAuC,QAAA,EAAI;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACf3C,OAAA;cAAAuC,QAAA,EAAI;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChB3C,OAAA;cAAAuC,QAAA,EAAI;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACR3C,OAAA;UAAAuC,QAAA,EACGpC,YAAY,CAAC6C,GAAG,CAAEC,WAAW,iBAC5BjD,OAAA;YAAAuC,QAAA,gBACEvC,OAAA;cAAAuC,QAAA,EAAKV,UAAU,CAACoB,WAAW,CAACC,eAAe;YAAC;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAClD3C,OAAA;cAAAuC,QAAA,EAAKU,WAAW,CAACE;YAAQ;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC/B3C,OAAA;cAAAuC,QAAA,EACGU,WAAW,CAACG,MAAM,GACjB,OAAOH,WAAW,CAACG,MAAM,CAACC,SAAS,IAAIJ,WAAW,CAACG,MAAM,CAACE,QAAQ,EAAE,GACpE;YAAK;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEL,CAAC,eACL3C,OAAA;cAAAuC,QAAA,EACGU,WAAW,CAACM,UAAU,GACrBN,WAAW,CAACM,UAAU,CAACC,IAAI,GAC3B;YAAK;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEL,CAAC,eACL3C,OAAA;cAAAuC,QAAA,gBACEvC,OAAA;gBAAMmC,KAAK,EAAE;kBACXC,KAAK,EAAEF,cAAc,CAACe,WAAW,CAACxB,MAAM,CAAC;kBACzCgC,UAAU,EAAE,MAAM;kBAClBC,aAAa,EAAE;gBACjB,CAAE;gBAAAnB,QAAA,EACCU,WAAW,CAACxB;cAAM;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,EACNM,WAAW,CAACU,WAAW,iBACtB3D,OAAA;gBAAKmC,KAAK,EAAE;kBAAEyB,QAAQ,EAAE,QAAQ;kBAAExB,KAAK,EAAE;gBAAU,CAAE;gBAAAG,QAAA,EAAC;cAEtD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACL3C,OAAA;cAAAuC,QAAA,eACEvC,OAAA;gBAAMmC,KAAK,EAAE;kBAAEC,KAAK,EAAEa,WAAW,CAACY,OAAO,GAAG,SAAS,GAAG;gBAAU,CAAE;gBAAAtB,QAAA,EACjEU,WAAW,CAACY,OAAO,GAAG,KAAK,GAAG;cAAI;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACL3C,OAAA;cAAAuC,QAAA,eACEvC,OAAA;gBACE8D,OAAO,EAAEA,CAAA,KAAM3C,UAAU,CAAC8B,WAAW,CAACc,GAAG,CAAE;gBAC3C5B,KAAK,EAAE;kBACL6B,eAAe,EAAE,SAAS;kBAC1B5B,KAAK,EAAE,OAAO;kBACd6B,MAAM,EAAE,MAAM;kBACd3B,OAAO,EAAE,aAAa;kBACtB4B,YAAY,EAAE,KAAK;kBACnBC,MAAM,EAAE;gBACV,CAAE;gBAAA5B,QAAA,EACH;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC;UAAA,GAhDEM,WAAW,CAACc,GAAG;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAiDpB,CACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACN,EAGApC,cAAc,iBACbP,OAAA;MAAKmC,KAAK,EAAE;QACViC,QAAQ,EAAE,OAAO;QACjBC,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACTR,eAAe,EAAE,iBAAiB;QAClCS,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,QAAQ;QACxBC,UAAU,EAAE,QAAQ;QACpBC,MAAM,EAAE;MACV,CAAE;MAAArC,QAAA,eACAvC,OAAA;QAAKmC,KAAK,EAAE;UACV6B,eAAe,EAAE,SAAS;UAC1B1B,OAAO,EAAE,MAAM;UACf4B,YAAY,EAAE,KAAK;UACnBW,QAAQ,EAAE,OAAO;UACjBC,SAAS,EAAE,MAAM;UACjBC,QAAQ,EAAE,MAAM;UAChB3C,KAAK,EAAE;QACT,CAAE;QAAAG,QAAA,gBACAvC,OAAA;UAAImC,KAAK,EAAE;YAAEC,KAAK,EAAE,SAAS;YAAEQ,YAAY,EAAE;UAAO,CAAE;UAAAL,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAE1E3C,OAAA;UAAKmC,KAAK,EAAE;YAAES,YAAY,EAAE;UAAO,CAAE;UAAAL,QAAA,gBACnCvC,OAAA;YAAAuC,QAAA,EAAQ;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACpC,cAAc,CAACkB,MAAM;QAAA;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eAEN3C,OAAA;UAAKmC,KAAK,EAAE;YAAES,YAAY,EAAE;UAAO,CAAE;UAAAL,QAAA,gBACnCvC,OAAA;YAAAuC,QAAA,EAAQ;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACpC,cAAc,CAACyE,cAAc;QAAA;UAAAxC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC,EAELpC,cAAc,CAAC0E,iBAAiB,IAAI1E,cAAc,CAAC0E,iBAAiB,CAACpC,MAAM,GAAG,CAAC,iBAC9E7C,OAAA;UAAKmC,KAAK,EAAE;YAAES,YAAY,EAAE;UAAO,CAAE;UAAAL,QAAA,gBACnCvC,OAAA;YAAAuC,QAAA,EAAQ;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACpC3C,OAAA;YAAAuC,QAAA,EACGhC,cAAc,CAAC0E,iBAAiB,CAACjC,GAAG,CAAC,CAACkC,OAAO,EAAEC,KAAK,kBACnDnF,OAAA;cAAAuC,QAAA,GAAiB2C,OAAO,CAACE,KAAK,EAAC,IAAE,EAACF,OAAO,CAACG,KAAK;YAAA,GAAtCF,KAAK;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAsC,CACrD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CACN,EAEApC,cAAc,CAAC+E,SAAS,IAAI/E,cAAc,CAAC+E,SAAS,CAACzC,MAAM,GAAG,CAAC,iBAC9D7C,OAAA;UAAKmC,KAAK,EAAE;YAAES,YAAY,EAAE;UAAO,CAAE;UAAAL,QAAA,gBACnCvC,OAAA;YAAAuC,QAAA,EAAQ;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACpC,cAAc,CAAC+E,SAAS,CAACC,IAAI,CAAC,IAAI,CAAC;QAAA;UAAA/C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CACN,EAEApC,cAAc,CAACiF,WAAW,iBACzBxF,OAAA;UAAKmC,KAAK,EAAE;YAAES,YAAY,EAAE;UAAO,CAAE;UAAAL,QAAA,gBACnCvC,OAAA;YAAAuC,QAAA,EAAQ;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACpC,cAAc,CAACiF,WAAW;QAAA;UAAAhD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CACN,EAEApC,cAAc,CAACkF,YAAY,iBAC1BzF,OAAA;UAAKmC,KAAK,EAAE;YAAES,YAAY,EAAE;UAAO,CAAE;UAAAL,QAAA,gBACnCvC,OAAA;YAAAuC,QAAA,EAAQ;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACpC,cAAc,CAACkF,YAAY;QAAA;UAAAjD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CACN,EAEApC,cAAc,CAACmF,QAAQ,iBACtB1F,OAAA;UAAKmC,KAAK,EAAE;YAAES,YAAY,EAAE;UAAO,CAAE;UAAAL,QAAA,gBACnCvC,OAAA;YAAAuC,QAAA,EAAQ;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACpC,cAAc,CAACmF,QAAQ;QAAA;UAAAlD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CACN,EAEApC,cAAc,CAACoF,aAAa,iBAC3B3F,OAAA;UAAKmC,KAAK,EAAE;YAAES,YAAY,EAAE;UAAO,CAAE;UAAAL,QAAA,gBACnCvC,OAAA;YAAAuC,QAAA,EAAQ;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACpC,cAAc,CAACoF,aAAa;QAAA;UAAAnD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CACN,EAEApC,cAAc,CAACqF,KAAK,iBACnB5F,OAAA;UAAKmC,KAAK,EAAE;YAAES,YAAY,EAAE;UAAO,CAAE;UAAAL,QAAA,gBACnCvC,OAAA;YAAAuC,QAAA,EAAQ;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACpC,cAAc,CAACqF,KAAK;QAAA;UAAApD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CACN,eAED3C,OAAA;UACE8D,OAAO,EAAEA,CAAA,KAAMtD,iBAAiB,CAAC,IAAI,CAAE;UACvC2B,KAAK,EAAE;YACL6B,eAAe,EAAE,SAAS;YAC1B5B,KAAK,EAAE,OAAO;YACd6B,MAAM,EAAE,MAAM;YACd3B,OAAO,EAAE,aAAa;YACtB4B,YAAY,EAAE,KAAK;YACnBC,MAAM,EAAE,SAAS;YACjB0B,SAAS,EAAE;UACb,CAAE;UAAAtD,QAAA,EACH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACzC,EAAA,CAhPID,sBAAsB;EAAA,QAITH,OAAO;AAAA;AAAAgG,EAAA,GAJpB7F,sBAAsB;AAkP5B,eAAeA,sBAAsB;AAAC,IAAA6F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}