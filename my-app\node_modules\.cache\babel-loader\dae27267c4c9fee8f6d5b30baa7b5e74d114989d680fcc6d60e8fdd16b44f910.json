{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\my-app\\\\my-app\\\\src\\\\components\\\\ProtectedRoute.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Navigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProtectedRoute = ({\n  children,\n  requiredRole = null\n}) => {\n  _s();\n  const {\n    user,\n    loading\n  } = useAuth();\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: \"Loading...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 12\n    }, this);\n  }\n  if (!user) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/login\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 12\n    }, this);\n  }\n  if (requiredRole) {\n    // Handle both single role (string) and multiple roles (array)\n    const allowedRoles = Array.isArray(requiredRole) ? requiredRole : [requiredRole];\n    if (!allowedRoles.includes(user.role)) {\n      return /*#__PURE__*/_jsxDEV(Navigate, {\n        to: \"/\",\n        replace: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 14\n      }, this);\n    }\n  }\n  return children;\n};\n_s(ProtectedRoute, \"EmJkapf7qiLC5Br5eCoEq4veZes=\", false, function () {\n  return [useAuth];\n});\n_c = ProtectedRoute;\nexport default ProtectedRoute;\nvar _c;\n$RefreshReg$(_c, \"ProtectedRoute\");", "map": {"version": 3, "names": ["React", "Navigate", "useAuth", "jsxDEV", "_jsxDEV", "ProtectedRoute", "children", "requiredRole", "_s", "user", "loading", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "replace", "allowedRoles", "Array", "isArray", "includes", "role", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/my-app/my-app/src/components/ProtectedRoute.js"], "sourcesContent": ["import React from 'react';\nimport { Navigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\n\nconst ProtectedRoute = ({ children, requiredRole = null }) => {\n  const { user, loading } = useAuth();\n\n  if (loading) {\n    return <div>Loading...</div>;\n  }\n\n  if (!user) {\n    return <Navigate to=\"/login\" replace />;\n  }\n\n  if (requiredRole) {\n    // Handle both single role (string) and multiple roles (array)\n    const allowedRoles = Array.isArray(requiredRole) ? requiredRole : [requiredRole];\n    if (!allowedRoles.includes(user.role)) {\n      return <Navigate to=\"/\" replace />;\n    }\n  }\n\n  return children;\n};\n\nexport default ProtectedRoute;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,cAAc,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,YAAY,GAAG;AAAK,CAAC,KAAK;EAAAC,EAAA;EAC5D,MAAM;IAAEC,IAAI;IAAEC;EAAQ,CAAC,GAAGR,OAAO,CAAC,CAAC;EAEnC,IAAIQ,OAAO,EAAE;IACX,oBAAON,OAAA;MAAAE,QAAA,EAAK;IAAU;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAC9B;EAEA,IAAI,CAACL,IAAI,EAAE;IACT,oBAAOL,OAAA,CAACH,QAAQ;MAACc,EAAE,EAAC,QAAQ;MAACC,OAAO;IAAA;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACzC;EAEA,IAAIP,YAAY,EAAE;IAChB;IACA,MAAMU,YAAY,GAAGC,KAAK,CAACC,OAAO,CAACZ,YAAY,CAAC,GAAGA,YAAY,GAAG,CAACA,YAAY,CAAC;IAChF,IAAI,CAACU,YAAY,CAACG,QAAQ,CAACX,IAAI,CAACY,IAAI,CAAC,EAAE;MACrC,oBAAOjB,OAAA,CAACH,QAAQ;QAACc,EAAE,EAAC,GAAG;QAACC,OAAO;MAAA;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IACpC;EACF;EAEA,OAAOR,QAAQ;AACjB,CAAC;AAACE,EAAA,CApBIH,cAAc;EAAA,QACQH,OAAO;AAAA;AAAAoB,EAAA,GAD7BjB,cAAc;AAsBpB,eAAeA,cAAc;AAAC,IAAAiB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}