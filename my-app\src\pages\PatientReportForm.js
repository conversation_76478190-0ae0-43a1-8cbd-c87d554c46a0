import React, { useState, useEffect } from 'react';
import axios from 'axios';
import './FormStyles.css';

const PatientReportForm = ({ appointmentId, onClose, onSave }) => {
  const [report, setReport] = useState({
    status: '',
    diseaseDetails: '',
    importantMeasures: [{ label: '', value: '' }],
    allergies: [],
    medications: '',
    followUpPlan: '',
    labTests: '',
    dietaryAdvice: '',
    notes: ''
  });
  const [loading, setLoading] = useState(false);
  const [isEditing, setIsEditing] = useState(false);

  useEffect(() => {
    fetchExistingReport();
  }, [appointmentId]);

  const fetchExistingReport = async () => {
    try {
      const response = await axios.get(`http://localhost:5000/api/patient-reports/appointment/${appointmentId}`);
      setReport(response.data.report);
      setIsEditing(true);
    } catch (error) {
      if (error.response?.status === 404) {
        // No existing report, create new one
        setIsEditing(false);
      } else {
        console.error('Error fetching report:', error);
      }
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setReport(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleMeasureChange = (index, field, value) => {
    const newMeasures = [...report.importantMeasures];
    newMeasures[index][field] = value;
    setReport(prev => ({
      ...prev,
      importantMeasures: newMeasures
    }));
  };

  const addMeasure = () => {
    setReport(prev => ({
      ...prev,
      importantMeasures: [...prev.importantMeasures, { label: '', value: '' }]
    }));
  };

  const removeMeasure = (index) => {
    const newMeasures = report.importantMeasures.filter((_, i) => i !== index);
    setReport(prev => ({
      ...prev,
      importantMeasures: newMeasures
    }));
  };

  const handleAllergiesChange = (e) => {
    const allergies = e.target.value.split(',').map(allergy => allergy.trim()).filter(allergy => allergy);
    setReport(prev => ({
      ...prev,
      allergies
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      const url = isEditing 
        ? `http://localhost:5000/api/patient-reports/${report._id}`
        : `http://localhost:5000/api/patient-reports/create`;
      
      const method = isEditing ? 'put' : 'post';
      
      const reportData = isEditing ? report : { ...report, appointmentId };

      const response = await axios[method](url, reportData);

      alert(isEditing ? 'Report updated successfully!' : 'Report created successfully!');
      onSave && onSave(response.data.report);
      onClose && onClose();
    } catch (error) {
      console.error('Error saving report:', error);
      alert('Error saving report');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0,0,0,0.8)',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 1000
    }}>
      <div style={{
        backgroundColor: '#1e1e1e',
        padding: '2rem',
        borderRadius: '8px',
        maxWidth: '800px',
        maxHeight: '90vh',
        overflow: 'auto',
        color: 'white',
        width: '90%'
      }}>
        <h2 style={{ color: '#BB86FC', marginBottom: '1rem' }}>
          {isEditing ? 'Edit Patient Report' : 'Create Patient Report'}
        </h2>

        <form onSubmit={handleSubmit}>
          <label>Patient Status</label>
          <select
            name="status"
            value={report.status}
            onChange={handleInputChange}
            required
          >
            <option value="">Select Status</option>
            <option value="Critical">Critical</option>
            <option value="Stable">Stable</option>
            <option value="Improving">Improving</option>
            <option value="Discharged">Discharged</option>
          </select>

          <label>Disease Details</label>
          <textarea
            name="diseaseDetails"
            value={report.diseaseDetails}
            onChange={handleInputChange}
            placeholder="Describe the diagnosis and condition"
            required
            rows="3"
          />

          <label>Important Measures</label>
          {report.importantMeasures.map((measure, index) => (
            <div key={index} style={{ display: 'flex', gap: '1rem', marginBottom: '0.5rem' }}>
              <input
                type="text"
                placeholder="Measure (e.g., Blood Pressure)"
                value={measure.label}
                onChange={(e) => handleMeasureChange(index, 'label', e.target.value)}
                style={{ flex: 1 }}
              />
              <input
                type="text"
                placeholder="Value (e.g., 120/80)"
                value={measure.value}
                onChange={(e) => handleMeasureChange(index, 'value', e.target.value)}
                style={{ flex: 1 }}
              />
              {report.importantMeasures.length > 1 && (
                <button
                  type="button"
                  onClick={() => removeMeasure(index)}
                  style={{
                    backgroundColor: '#dc3545',
                    color: 'white',
                    border: 'none',
                    padding: '0.5rem',
                    borderRadius: '4px'
                  }}
                >
                  Remove
                </button>
              )}
            </div>
          ))}
          <button
            type="button"
            onClick={addMeasure}
            style={{
              backgroundColor: '#28a745',
              color: 'white',
              border: 'none',
              padding: '0.5rem 1rem',
              borderRadius: '4px',
              marginBottom: '1rem'
            }}
          >
            Add Measure
          </button>

          <label>Allergies (comma-separated)</label>
          <input
            type="text"
            placeholder="e.g., Penicillin, Peanuts, Latex"
            value={report.allergies.join(', ')}
            onChange={handleAllergiesChange}
          />

          <label>Medications</label>
          <textarea
            name="medications"
            value={report.medications}
            onChange={handleInputChange}
            placeholder="Current medications and dosages"
            rows="2"
          />

          <label>Follow-up Plan</label>
          <textarea
            name="followUpPlan"
            value={report.followUpPlan}
            onChange={handleInputChange}
            placeholder="Next steps and follow-up instructions"
            rows="2"
          />

          <label>Lab Tests</label>
          <textarea
            name="labTests"
            value={report.labTests}
            onChange={handleInputChange}
            placeholder="Required lab tests and investigations"
            rows="2"
          />

          <label>Dietary Advice</label>
          <textarea
            name="dietaryAdvice"
            value={report.dietaryAdvice}
            onChange={handleInputChange}
            placeholder="Nutrition and dietary recommendations"
            rows="2"
          />

          <label>Doctor's Notes</label>
          <textarea
            name="notes"
            value={report.notes}
            onChange={handleInputChange}
            placeholder="Additional notes and observations"
            rows="3"
          />

          <div style={{ display: 'flex', gap: '1rem', marginTop: '1rem' }}>
            <button
              type="submit"
              disabled={loading}
              style={{
                backgroundColor: '#007BFF',
                color: 'white',
                border: 'none',
                padding: '0.75rem 1.5rem',
                borderRadius: '4px',
                cursor: 'pointer',
                flex: 1
              }}
            >
              {loading ? 'Saving...' : (isEditing ? 'Update Report' : 'Create Report')}
            </button>
            <button
              type="button"
              onClick={onClose}
              style={{
                backgroundColor: '#6c757d',
                color: 'white',
                border: 'none',
                padding: '0.75rem 1.5rem',
                borderRadius: '4px',
                cursor: 'pointer',
                flex: 1
              }}
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default PatientReportForm;
