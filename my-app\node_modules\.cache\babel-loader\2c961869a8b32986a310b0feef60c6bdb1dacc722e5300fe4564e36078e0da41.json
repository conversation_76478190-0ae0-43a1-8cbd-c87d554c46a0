{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\my-app\\\\my-app\\\\src\\\\pages\\\\Signup.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport './FormStyles.css';\nimport vid from '../images/backgroundvideo.mp4';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Signup() {\n  _s();\n  const [formData, setFormData] = useState({\n    name: \"\",\n    email: \"\",\n    password: \"\",\n    role: \"User\"\n  });\n  const {\n    signup\n  } = useAuth();\n  const navigate = useNavigate();\n  const handleChange = e => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    console.log('Signup form data:', formData);\n\n    // Validate form data\n    if (!formData.name || !formData.email || !formData.password) {\n      alert('Please fill in all fields');\n      return;\n    }\n    const result = await signup(formData);\n    console.log('Signup result:', result);\n    if (result.success) {\n      alert(\"Signup successful! Please login with your credentials.\");\n      // Redirect to login page\n      navigate('/login');\n    } else {\n      alert(`Signup failed: ${result.message}`);\n    }\n  };\n  window.addEventListener('load', function () {\n    const video = document.getElementById('myVideo');\n    video.play();\n  });\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      children: /*#__PURE__*/_jsxDEV(\"section\", {\n        class: \"showcase\",\n        children: [/*#__PURE__*/_jsxDEV(\"video\", {\n          id: \"myVideo\",\n          loop: true,\n          muted: true,\n          children: /*#__PURE__*/_jsxDEV(\"source\", {\n            src: vid,\n            type: \"video/mp4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 23\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          id: \"head\",\n          children: \"SIGNUP\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 23\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 19\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 8\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: /*#__PURE__*/_jsxDEV(\"p\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        name: \"name\",\n        placeholder: \"Name\",\n        onChange: handleChange\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"email\",\n        name: \"email\",\n        placeholder: \"Email\",\n        onChange: handleChange\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"password\",\n        name: \"password\",\n        placeholder: \"Password\",\n        onChange: handleChange\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n        name: \"role\",\n        onChange: handleChange,\n        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"User\",\n          children: \"User\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"Doctor\",\n          children: \"Doctor\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        children: \"Sign Up\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 48,\n    columnNumber: 5\n  }, this);\n}\n_s(Signup, \"tjWDJHk3WuJ64IsdOzUHgxN0nPo=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = Signup;\nexport default Signup;\nvar _c;\n$RefreshReg$(_c, \"Signup\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "useAuth", "vid", "jsxDEV", "_jsxDEV", "Signup", "_s", "formData", "setFormData", "name", "email", "password", "role", "signup", "navigate", "handleChange", "e", "target", "value", "handleSubmit", "preventDefault", "console", "log", "alert", "result", "success", "message", "window", "addEventListener", "video", "document", "getElementById", "play", "children", "class", "id", "loop", "muted", "src", "type", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "placeholder", "onChange", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/my-app/my-app/src/pages/Signup.js"], "sourcesContent": ["import React from 'react'\r\nimport { useState } from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport { useAuth } from '../context/AuthContext';\r\nimport './FormStyles.css';\r\nimport vid from '../images/backgroundvideo.mp4'\r\n\r\nfunction Signup() {\r\n  const [formData, setFormData] = useState({\r\n    name: \"\",\r\n    email: \"\",\r\n    password: \"\",\r\n    role: \"User\",\r\n  });\r\n  const { signup } = useAuth();\r\n  const navigate = useNavigate();\r\n\r\n  const handleChange = (e) => {\r\n    setFormData({ ...formData, [e.target.name]: e.target.value });\r\n  };\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    console.log('Signup form data:', formData);\r\n\r\n    // Validate form data\r\n    if (!formData.name || !formData.email || !formData.password) {\r\n      alert('Please fill in all fields');\r\n      return;\r\n    }\r\n\r\n    const result = await signup(formData);\r\n    console.log('Signup result:', result);\r\n\r\n    if (result.success) {\r\n      alert(\"Signup successful! Please login with your credentials.\");\r\n      // Redirect to login page\r\n      navigate('/login');\r\n    } else {\r\n      alert(`Signup failed: ${result.message}`);\r\n    }\r\n  };\r\n  window.addEventListener('load', function() {\r\n    const video = document.getElementById('myVideo');\r\n    video.play();\r\n  });\r\n  return (\r\n    <div>\r\n       <div>\r\n                  <section class=\"showcase\">\r\n                      <video id=\"myVideo\" loop muted>\r\n                        <source src={vid} type=\"video/mp4\"/>\r\n                      </video>\r\n                      <h1 id=\"head\">SIGNUP</h1>\r\n                    </section>\r\n      \r\n                  </div>\r\n      <div>\r\n        <p>\r\n\r\n\r\n\r\n\r\n        </p>\r\n      </div>\r\n      <form onSubmit={handleSubmit}>\r\n      <input type=\"text\" name=\"name\" placeholder=\"Name\" onChange={handleChange} />\r\n      <input type=\"email\" name=\"email\" placeholder=\"Email\" onChange={handleChange} />\r\n      <input type=\"password\" name=\"password\" placeholder=\"Password\" onChange={handleChange} />\r\n      <select name=\"role\" onChange={handleChange}>\r\n        <option value=\"User\">User</option>\r\n        <option value=\"Doctor\">Doctor</option>\r\n      </select>\r\n      <button type=\"submit\">Sign Up</button>\r\n    </form>\r\n    </div>\r\n    \r\n  );\r\n}\r\n\r\nexport default Signup;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,QAAQ,OAAO;AAChC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAO,kBAAkB;AACzB,OAAOC,GAAG,MAAM,+BAA+B;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAE/C,SAASC,MAAMA,CAAA,EAAG;EAAAC,EAAA;EAChB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAC;IACvCU,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;IAAEC;EAAO,CAAC,GAAGZ,OAAO,CAAC,CAAC;EAC5B,MAAMa,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAE9B,MAAMe,YAAY,GAAIC,CAAC,IAAK;IAC1BR,WAAW,CAAC;MAAE,GAAGD,QAAQ;MAAE,CAACS,CAAC,CAACC,MAAM,CAACR,IAAI,GAAGO,CAAC,CAACC,MAAM,CAACC;IAAM,CAAC,CAAC;EAC/D,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOH,CAAC,IAAK;IAChCA,CAAC,CAACI,cAAc,CAAC,CAAC;IAClBC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEf,QAAQ,CAAC;;IAE1C;IACA,IAAI,CAACA,QAAQ,CAACE,IAAI,IAAI,CAACF,QAAQ,CAACG,KAAK,IAAI,CAACH,QAAQ,CAACI,QAAQ,EAAE;MAC3DY,KAAK,CAAC,2BAA2B,CAAC;MAClC;IACF;IAEA,MAAMC,MAAM,GAAG,MAAMX,MAAM,CAACN,QAAQ,CAAC;IACrCc,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEE,MAAM,CAAC;IAErC,IAAIA,MAAM,CAACC,OAAO,EAAE;MAClBF,KAAK,CAAC,wDAAwD,CAAC;MAC/D;MACAT,QAAQ,CAAC,QAAQ,CAAC;IACpB,CAAC,MAAM;MACLS,KAAK,CAAC,kBAAkBC,MAAM,CAACE,OAAO,EAAE,CAAC;IAC3C;EACF,CAAC;EACDC,MAAM,CAACC,gBAAgB,CAAC,MAAM,EAAE,YAAW;IACzC,MAAMC,KAAK,GAAGC,QAAQ,CAACC,cAAc,CAAC,SAAS,CAAC;IAChDF,KAAK,CAACG,IAAI,CAAC,CAAC;EACd,CAAC,CAAC;EACF,oBACE5B,OAAA;IAAA6B,QAAA,gBACG7B,OAAA;MAAA6B,QAAA,eACW7B,OAAA;QAAS8B,KAAK,EAAC,UAAU;QAAAD,QAAA,gBACrB7B,OAAA;UAAO+B,EAAE,EAAC,SAAS;UAACC,IAAI;UAACC,KAAK;UAAAJ,QAAA,eAC5B7B,OAAA;YAAQkC,GAAG,EAAEpC,GAAI;YAACqC,IAAI,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,eACRvC,OAAA;UAAI+B,EAAE,EAAC,MAAM;UAAAF,QAAA,EAAC;QAAM;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEP,CAAC,eAClBvC,OAAA;MAAA6B,QAAA,eACE7B,OAAA;QAAAoC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAKG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eACNvC,OAAA;MAAMwC,QAAQ,EAAEzB,YAAa;MAAAc,QAAA,gBAC7B7B,OAAA;QAAOmC,IAAI,EAAC,MAAM;QAAC9B,IAAI,EAAC,MAAM;QAACoC,WAAW,EAAC,MAAM;QAACC,QAAQ,EAAE/B;MAAa;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC5EvC,OAAA;QAAOmC,IAAI,EAAC,OAAO;QAAC9B,IAAI,EAAC,OAAO;QAACoC,WAAW,EAAC,OAAO;QAACC,QAAQ,EAAE/B;MAAa;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC/EvC,OAAA;QAAOmC,IAAI,EAAC,UAAU;QAAC9B,IAAI,EAAC,UAAU;QAACoC,WAAW,EAAC,UAAU;QAACC,QAAQ,EAAE/B;MAAa;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACxFvC,OAAA;QAAQK,IAAI,EAAC,MAAM;QAACqC,QAAQ,EAAE/B,YAAa;QAAAkB,QAAA,gBACzC7B,OAAA;UAAQc,KAAK,EAAC,MAAM;UAAAe,QAAA,EAAC;QAAI;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAClCvC,OAAA;UAAQc,KAAK,EAAC,QAAQ;UAAAe,QAAA,EAAC;QAAM;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CAAC,eACTvC,OAAA;QAAQmC,IAAI,EAAC,QAAQ;QAAAN,QAAA,EAAC;MAAO;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAGV;AAACrC,EAAA,CAvEQD,MAAM;EAAA,QAOMJ,OAAO,EACTD,WAAW;AAAA;AAAA+C,EAAA,GARrB1C,MAAM;AAyEf,eAAeA,MAAM;AAAC,IAAA0C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}