import React from 'react';
import { BrowserRouter as Router, Route, Routes } from 'react-router-dom';
import { AuthProvider } from './context/AuthContext';
import ProtectedRoute from './components/ProtectedRoute';
import Navbar from './pages/Navbar';
import Signup from './pages/Signup';
import Login from './pages/Login';
import Home from './pages/Home';
import Appointment from './pages/Appointment';
import AboutUs from './pages/AboutUs';
import Contact from './pages/Contact';
import UserAppointmentHistory from './pages/UserAppointmentHistory';
import Footer from './pages/Footer';
import AppointmentsTable from './pages/AppointmentsTable';
import DoctorInformationForm from './pages/DoctorInformationForm';

import DoctorDashboard2 from "./pages/DoctorDashboard2";
import AppointmentCalendar from "./pages/AppointmentCalendar";
import AppointmentAnalytics from "./pages/AppointmentAnalytics";
import Manage from "./pages/Manage";
import Analytics from './pages/Analytics';
import Messages from './pages/Messages';
import AddDepartment from './pages/AddDepartment';
import AdminDashboard from './pages/AdminDashboard';

function App() {
  return (
    <AuthProvider>
      <Router>
        <Navbar />
        <Routes>
          {/* Public Routes */}
          <Route path="/" element={<Home />} />
          <Route path="/about" element={<AboutUs />} />
          <Route path="/signup" element={<Signup />} />
          <Route path="/login" element={<Login />} />

          {/* User Only Routes */}
          <Route path="/my-appointments" element={
            <ProtectedRoute requiredRole="User">
              <UserAppointmentHistory />
            </ProtectedRoute>
          } />

          {/* Routes for Users and Doctors (both can book appointments) */}
          <Route path="/appointment" element={
            <ProtectedRoute requiredRole={["User", "Doctor"]}>
              <Appointment />
            </ProtectedRoute>
          } />

          {/* Routes for Users and Doctors */}
          <Route path="/contact" element={
            <ProtectedRoute>
              <Contact />
            </ProtectedRoute>
          } />

          {/* Admin Only Routes */}
          <Route path="/admin" element={
            <ProtectedRoute requiredRole="Admin">
              <AdminDashboard />
            </ProtectedRoute>
          } />
          <Route path="/appotable" element={
            <ProtectedRoute requiredRole="Doctor">
              <AppointmentsTable />
            </ProtectedRoute>
          } />
          <Route path="/doctorinfo" element={
            <ProtectedRoute requiredRole="Doctor">
              <DoctorInformationForm />
            </ProtectedRoute>
          } />
          <Route path="/manage" element={
            <ProtectedRoute requiredRole="Admin">
              <Manage />
            </ProtectedRoute>
          } />
          <Route path="/analytics" element={
            <ProtectedRoute requiredRole="Admin">
              <Analytics />
            </ProtectedRoute>
          } />
          <Route path="/add-department" element={
            <ProtectedRoute requiredRole="Admin">
              <AddDepartment />
            </ProtectedRoute>
          } />
          <Route path="/messages" element={
            <ProtectedRoute requiredRole="Admin">
              <Messages />
            </ProtectedRoute>
          } />

          {/* Doctor Only Routes */}
          <Route path="/docdash" element={
            <ProtectedRoute requiredRole="Doctor">
              <DoctorDashboard2 />
            </ProtectedRoute>
          } />
          <Route path="/appointment-calendar" element={
            <ProtectedRoute requiredRole="Doctor">
              <AppointmentCalendar />
            </ProtectedRoute>
          } />
          <Route path="/appointment-analytics" element={
            <ProtectedRoute requiredRole="Doctor">
              <AppointmentAnalytics />
            </ProtectedRoute>
          } />
        </Routes>
        <Footer />
      </Router>
    </AuthProvider>
  );
}

export default App;


