const express = require("express");
const cors = require("cors");
const fs = require("fs");
const path = require("path");

const app = express();

// Middleware
app.use(express.json());
app.use(cors({
  origin: "http://localhost:3000",
  credentials: true
}));

// Persistent storage file paths
const DATA_DIR = path.join(__dirname, 'data');
const USERS_FILE = path.join(DATA_DIR, 'users.json');
const DOCTORS_FILE = path.join(DATA_DIR, 'doctors.json');
const APPOINTMENTS_FILE = path.join(DATA_DIR, 'appointments.json');
const REPORTS_FILE = path.join(DATA_DIR, 'reports.json');
const LEAVES_FILE = path.join(DATA_DIR, 'leaves.json');

// Ensure data directory exists
if (!fs.existsSync(DATA_DIR)) {
  fs.mkdirSync(DATA_DIR, { recursive: true });
}

// Storage functions
const loadData = (filePath, defaultData = []) => {
  try {
    if (fs.existsSync(filePath)) {
      const data = fs.readFileSync(filePath, 'utf8');
      return JSON.parse(data);
    }
  } catch (error) {
    console.error(`Error loading data from ${filePath}:`, error);
  }
  return defaultData;
};

const saveData = (filePath, data) => {
  try {
    fs.writeFileSync(filePath, JSON.stringify(data, null, 2));
  } catch (error) {
    console.error(`Error saving data to ${filePath}:`, error);
  }
};

// Load data from files
let appointments = loadData(APPOINTMENTS_FILE, []);
let patientReports = loadData(REPORTS_FILE, []);
let doctorLeaves = loadData(LEAVES_FILE, []);
let users = loadData(USERS_FILE, []);
let currentUser = null; // Track current logged-in user
let appointmentIdCounter = Math.max(...appointments.map(a => a.id || 0), 0) + 1;
let reportIdCounter = Math.max(...patientReports.map(r => r.id || 0), 0) + 1;
let leaveIdCounter = 1;

// Load doctors data
let doctors = loadData(DOCTORS_FILE, []);

// Sample data (only initialize if files don't exist)
let departments = [
  { _id: "dept1", name: "Cardiology", description: "Heart and cardiovascular system" },
  { _id: "dept2", name: "Neurology", description: "Brain and nervous system" },
  { _id: "dept3", name: "Pediatrics", description: "Children's health" }
];

// Initialize default data if files are empty
if (doctors.length === 0) {
  doctors = [
    { _id: "doc1", firstName: "John", lastName: "Smith", specialization: "Cardiology", department: "dept1", email: "<EMAIL>" },
    { _id: "doc2", firstName: "Jane", lastName: "Jones", specialization: "Neurology", department: "dept2", email: "<EMAIL>" },
    { _id: "doc3", firstName: "Robert", lastName: "Brown", specialization: "Neurology", department: "dept2", email: "<EMAIL>" },
    { _id: "doc4", firstName: "Sarah", lastName: "Williams", specialization: "Pediatrics", department: "dept3", email: "<EMAIL>" }
  ];
  saveData(DOCTORS_FILE, doctors);
}

if (users.length === 0) {
  users = [
    { id: 1, name: "Admin User", email: "<EMAIL>", password: "admin123", role: "Admin" },
    { id: 2, name: "Test Doctor", email: "<EMAIL>", password: "doctor123", role: "Doctor" },
    { id: 3, name: "Test User", email: "<EMAIL>", password: "user123", role: "User" }
  ];
  saveData(USERS_FILE, users);
}

// Simple auth middleware (moved here before routes)
const requireAuth = (req, res, next) => {
  if (!currentUser) {
    return res.status(401).json({ message: "Authentication required" });
  }
  req.user = currentUser;
  next();
};

const requireRole = (role) => {
  return (req, res, next) => {
    if (!currentUser) {
      return res.status(401).json({ message: "Authentication required" });
    }
    if (currentUser.role !== role) {
      return res.status(403).json({ message: "Access denied" });
    }
    req.user = currentUser;
    next();
  };
};

// Routes
app.get("/", (req, res) => {
  res.json({ message: "Hospital Management Backend API", status: "running" });
});



// Departments
app.get("/api/departments/all", (req, res) => {
  res.json({ departments });
});

// Doctors
app.get("/api/doctors/all", (req, res) => {
  res.json({ doctors });
});

app.get("/api/doctors/department/:departmentId", (req, res) => {
  const { departmentId } = req.params;
  const departmentDoctors = doctors.filter(doctor => doctor.department === departmentId);
  res.json({ doctors: departmentDoctors });
});

// Create new doctor (for DoctorInformationForm)
app.post("/api/doctors/create", requireAuth, (req, res) => {
  try {
    console.log(`📝 Doctor registration request received`);
    console.log(`👤 Current user:`, req.user);
    console.log(`📋 Request body:`, req.body);

    const {
      firstName,
      lastName,
      email,
      specialization,
      department,
      experience,
      qualification,
      licenseNumber
    } = req.body;

    console.log(`👨‍⚕️ Creating new doctor: ${firstName} ${lastName}`);
    console.log(`📧 Email: ${email}, Department: ${department}`);

    // Validate required fields
    if (!firstName || !lastName || !email || !specialization || !department) {
      console.log(`❌ Missing required fields:`, {
        firstName: !!firstName,
        lastName: !!lastName,
        email: !!email,
        specialization: !!specialization,
        department: !!department
      });
      return res.status(400).json({ message: "Required fields missing" });
    }

    // Check if doctor already exists
    const existingDoctor = doctors.find(d => d.email === email);
    if (existingDoctor) {
      console.log(`❌ Doctor already exists with email: ${email}`);
      return res.status(400).json({ message: "Doctor with this email already exists" });
    }

    // Create new doctor
    const newDoctor = {
      _id: `doc${doctors.length + 1}`,
      firstName,
      lastName,
      email,
      specialization,
      department,
      experience: experience || '',
      qualification: qualification || '',
      licenseNumber: licenseNumber || '',
      createdAt: new Date().toISOString()
    };

    console.log(`🏥 New doctor object:`, newDoctor);
    doctors.push(newDoctor);
    saveData(DOCTORS_FILE, doctors); // Save to file
    console.log(`💾 Doctor saved to file. Total doctors: ${doctors.length}`);

    console.log(`✅ Doctor created successfully: ${firstName} ${lastName}`);
    res.status(201).json({
      message: "Doctor registered successfully",
      doctor: newDoctor
    });
  } catch (error) {
    console.error('❌ Error creating doctor:', error);
    res.status(500).json({ message: "Error creating doctor", error: error.message });
  }
});

// Available slots
app.get("/api/appointments/available-slots", (req, res) => {
  const allSlots = [
    "9:00 AM - 10:00 AM", "10:00 AM - 11:00 AM", "11:00 AM - 12:00 PM",
    "12:00 PM - 1:00 PM", "1:00 PM - 2:00 PM", "2:00 PM - 3:00 PM",
    "3:00 PM - 4:00 PM", "4:00 PM - 5:00 PM"
  ];

  const availableSlots = allSlots.map(slot => ({
    timeSlot: slot,
    available: true,
    currentBookings: Math.floor(Math.random() * 2),
    maxCapacity: 4
  }));

  res.json({ availableSlots });
});

// Create appointment
app.post("/api/appointments/create", (req, res) => {
  try {
    const {
      firstName, lastName, email, mobileNumber, nic, gender,
      address, dob, department, doctor, appointmentDate, timeSlot
    } = req.body;

    const dept = departments.find(d => d._id === department);
    const doc = doctors.find(d => d._id === doctor);

    const newAppointment = {
      _id: appointmentIdCounter++,
      firstName, lastName, email, mobileNumber, nic, gender,
      address, dob, department: dept, doctor: doc,
      appointmentDate, timeSlot,
      status: 'pending',
      visited: false,
      createdAt: new Date().toISOString()
    };

    appointments.push(newAppointment);
    saveData(APPOINTMENTS_FILE, appointments); // Save to file
    console.log(`✅ New appointment created for ${firstName} ${lastName}`);
    
    res.status(201).json({ 
      message: "Appointment booked successfully",
      appointment: newAppointment
    });
  } catch (error) {
    console.error("❌ Error creating appointment:", error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
});



// Get all appointments (protected)
app.get("/api/appointments/all", requireAuth, (req, res) => {
  console.log(`📋 Fetching ${appointments.length} appointments for ${req.user.email}`);
  res.json({ appointments });
});

// Get appointments by user email (protected)
app.get("/api/appointments/user/:email", requireAuth, (req, res) => {
  const { email } = req.params;

  // Users can only see their own appointments, admins can see all
  if (req.user.role !== 'Admin' && req.user.email !== email) {
    return res.status(403).json({ message: "Access denied" });
  }

  const userAppointments = appointments.filter(apt => apt.email === email);
  console.log(`📋 Fetching ${userAppointments.length} appointments for user: ${email}`);
  res.json({ appointments: userAppointments });
});

// Get system statistics (admin only)
app.get("/api/system/stats", requireRole('Admin'), (req, res) => {
  const uniqueUsers = new Set(appointments.map(apt => apt.email)).size;
  const stats = {
    totalAppointments: appointments.length,
    totalUsers: uniqueUsers,
    totalDoctors: doctors.length,
    totalDepartments: departments.length,
    totalReports: patientReports.length,
    totalLeaves: doctorLeaves.length
  };
  console.log(`📊 System stats requested by admin: ${req.user.email}`);
  res.json({ stats });
});

// Basic auth endpoints
app.post("/api/auth/signup", (req, res) => {
  const { name, email, password, role } = req.body;
  console.log(`📝 Signup: ${email} as ${role}`);

  // Validate fields
  if (!name || !email || !password || !role) {
    return res.status(400).json({ message: "All fields are required" });
  }

  // Check if user exists
  if (users.find(u => u.email === email)) {
    return res.status(400).json({ message: "User already exists" });
  }

  // Create user
  const newUser = { id: users.length + 1, name, email, password, role };
  users.push(newUser);
  saveData(USERS_FILE, users); // Save to file
  console.log(`✅ User created: ${email}`);

  // Add to doctors array if doctor
  if (role === 'Doctor') {
    const newDoctor = {
      _id: `doc${doctors.length + 1}`,
      firstName: name.split(' ')[0] || name,
      lastName: name.split(' ')[1] || '',
      email,
      specialization: 'General Medicine',
      department: 'dept1'
    };
    doctors.push(newDoctor);
    saveData(DOCTORS_FILE, doctors); // Save to file
    console.log(`✅ Doctor added: ${newDoctor.firstName} ${newDoctor.lastName}`);
  }

  res.status(201).json({
    message: "Registration successful",
    user: { id: newUser.id, name: newUser.name, email: newUser.email, role: newUser.role }
  });
});

app.post("/api/auth/login", (req, res) => {
  const { email, password } = req.body;
  console.log(`🔐 Login attempt: ${email}`);
  console.log(`👥 Total users in system: ${users.length}`);
  console.log(`📋 Available users:`, users.map(u => ({ email: u.email, role: u.role })));

  // Find user in users array
  const user = users.find(u => u.email === email && u.password === password);

  if (!user) {
    console.log(`❌ Login failed: Invalid credentials for ${email}`);
    console.log(`🔍 Checking if email exists:`, users.find(u => u.email === email) ? 'Email found, wrong password' : 'Email not found');
    return res.status(401).json({ message: "Invalid credentials" });
  }

  console.log(`✅ Login successful: ${user.email} as ${user.role}`);
  // Set current user for session
  currentUser = { id: user.id, name: user.name, email: user.email, role: user.role };
  res.json({ message: "Login successful", user: currentUser });
});

app.get("/api/auth/verify", (req, res) => {
  if (currentUser) {
    console.log(`✅ User verified: ${currentUser.email}`);
    res.json({ message: "User authenticated", user: currentUser });
  } else {
    console.log("❌ No authenticated user");
    res.status(401).json({ message: "Not authenticated" });
  }
});

app.post("/api/auth/logout", (req, res) => {
  if (currentUser) {
    console.log(`🚪 User logged out: ${currentUser.email}`);
    currentUser = null; // Clear current user
  } else {
    console.log("🚪 Logout attempt (no user was logged in)");
  }
  res.json({ message: "Logged out successfully" });
});

// Patient Reports endpoints
app.post("/api/patient-reports/create", requireRole('Doctor'), (req, res) => {
  try {
    const newReport = {
      _id: reportIdCounter++,
      ...req.body,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    patientReports.push(newReport);
    saveData(REPORTS_FILE, patientReports); // Save to file
    console.log(`📄 Patient report created for appointment: ${req.body.appointmentId}`);
    res.status(201).json({ message: "Report created successfully", report: newReport });
  } catch (error) {
    res.status(500).json({ message: "Error creating report", error: error.message });
  }
});

app.get("/api/patient-reports/appointment/:appointmentId", requireAuth, (req, res) => {
  const { appointmentId } = req.params;
  const report = patientReports.find(r => r.appointmentId === appointmentId);
  if (report) {
    res.json({ report });
  } else {
    res.status(404).json({ message: "Report not found" });
  }
});

app.put("/api/patient-reports/:reportId", requireRole('Doctor'), (req, res) => {
  try {
    const { reportId } = req.params;
    const reportIndex = patientReports.findIndex(r => r._id == reportId);
    if (reportIndex !== -1) {
      patientReports[reportIndex] = {
        ...patientReports[reportIndex],
        ...req.body,
        updatedAt: new Date().toISOString()
      };
      console.log(`📄 Patient report updated: ${reportId}`);
      res.json({ message: "Report updated successfully", report: patientReports[reportIndex] });
    } else {
      res.status(404).json({ message: "Report not found" });
    }
  } catch (error) {
    res.status(500).json({ message: "Error updating report", error: error.message });
  }
});

// Doctor Leaves endpoints
app.post("/api/doctor-leaves/create", requireRole('Doctor'), (req, res) => {
  try {
    const { date, reason } = req.body;
    const newLeave = {
      _id: leaveIdCounter++,
      date,
      reason: reason || 'Personal Leave',
      createdAt: new Date().toISOString()
    };
    doctorLeaves.push(newLeave);
    saveData(LEAVES_FILE, doctorLeaves); // Save to file

    // Reschedule appointments on this date
    const affectedAppointments = appointments.filter(apt =>
      apt.appointmentDate.split('T')[0] === date
    );

    affectedAppointments.forEach(apt => {
      apt.status = 'rescheduled';
      apt.rescheduled = true;
      apt.originalDate = apt.appointmentDate;
      // In a real app, you'd reschedule to next available date
    });

    if (affectedAppointments.length > 0) {
      saveData(APPOINTMENTS_FILE, appointments); // Save updated appointments
    }

    console.log(`🏖️ Doctor leave marked for ${date}, ${affectedAppointments.length} appointments rescheduled`);
    res.status(201).json({
      message: "Leave marked successfully",
      leave: newLeave,
      rescheduledAppointments: affectedAppointments.length
    });
  } catch (error) {
    res.status(500).json({ message: "Error marking leave", error: error.message });
  }
});

app.get("/api/doctor-leaves/all", requireRole('Doctor'), (req, res) => {
  console.log(`🏖️ Fetching ${doctorLeaves.length} leave dates`);
  res.json({ leaves: doctorLeaves });
});

// Add sample appointments
const addSampleData = () => {
  const sampleAppointments = [
    {
      _id: appointmentIdCounter++,
      firstName: "John", lastName: "Doe", email: "<EMAIL>",
      mobileNumber: "1234567890", nic: "123456789V", gender: "male",
      address: "123 Main St", dob: "1990-01-15",
      department: departments[0], doctor: doctors[0],
      appointmentDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      timeSlot: "9:00 AM - 10:00 AM", status: 'pending', visited: false,
      createdAt: new Date().toISOString()
    },
    {
      _id: appointmentIdCounter++,
      firstName: "Jane", lastName: "Smith", email: "<EMAIL>",
      mobileNumber: "0987654321", nic: "987654321V", gender: "female",
      address: "456 Oak Ave", dob: "1985-05-20",
      department: departments[1], doctor: doctors[1],
      appointmentDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(),
      timeSlot: "10:00 AM - 11:00 AM", status: 'pending', visited: false,
      createdAt: new Date().toISOString()
    }
  ];
  
  appointments.push(...sampleAppointments);
  console.log(`📝 Added ${sampleAppointments.length} sample appointments`);
};

// Start server
const PORT = 5000;
app.listen(PORT, () => {
  console.log(`🚀 Hospital Management Backend running on http://localhost:${PORT}`);
  console.log(`📊 API Status: http://localhost:${PORT}`);
  addSampleData();
}).on('error', (err) => {
  if (err.code === 'EADDRINUSE') {
    console.log(`❌ Port ${PORT} is already in use. Please close other applications using this port.`);
  } else {
    console.error('❌ Server error:', err);
  }
});
