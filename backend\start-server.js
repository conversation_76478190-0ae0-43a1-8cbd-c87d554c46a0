const express = require("express");
const cors = require("cors");

const app = express();

// Middleware
app.use(express.json());
app.use(cors({
  origin: "http://localhost:3000",
  credentials: true
}));

// In-memory storage
let appointments = [];
let patientReports = [];
let doctor<PERSON>eaves = [];
let users = []; // Store registered users
let currentUser = null; // Track current logged-in user
let appointmentIdCounter = 1;
let reportIdCounter = 1;
let leaveIdCounter = 1;

// Sample data
const departments = [
  { _id: "dept1", name: "Cardiology", description: "Heart and cardiovascular system" },
  { _id: "dept2", name: "Neurology", description: "Brain and nervous system" },
  { _id: "dept3", name: "Pediatrics", description: "Children's health" }
];

const doctors = [
  { _id: "doc1", firstName: "<PERSON>", lastName: "<PERSON>", specialization: "Cardiology", department: "dept1" },
  { _id: "doc2", firstName: "<PERSON>", lastName: "<PERSON>", specialization: "Neurology", department: "dept2" },
  { _id: "doc3", firstName: "<PERSON>", lastName: "<PERSON>", specialization: "Neurology", department: "dept2" },
  { _id: "doc4", firstName: "Sarah", lastName: "Williams", specialization: "Pediatrics", department: "dept3" }
];

// Initialize with default users (for testing)
users.push(
  { id: 1, name: "Admin User", email: "<EMAIL>", password: "admin123", role: "Admin" },
  { id: 2, name: "Test Doctor", email: "<EMAIL>", password: "doctor123", role: "Doctor" },
  { id: 3, name: "Test User", email: "<EMAIL>", password: "user123", role: "User" }
);

// Routes
app.get("/", (req, res) => {
  res.json({ message: "Hospital Management Backend API", status: "running" });
});

// Debug endpoint to check current state
app.get("/api/debug/state", (req, res) => {
  res.json({
    users: users.length,
    doctors: doctors.length,
    departments: departments.length,
    appointments: appointments.length,
    currentUser: currentUser ? currentUser.email : null,
    usersData: users,
    doctorsData: doctors,
    departmentsData: departments
  });
});

// Departments
app.get("/api/departments/all", (req, res) => {
  res.json({ departments });
});

// Doctors
app.get("/api/doctors/all", (req, res) => {
  res.json({ doctors });
});

app.get("/api/doctors/department/:departmentId", (req, res) => {
  const { departmentId } = req.params;
  const departmentDoctors = doctors.filter(doctor => doctor.department === departmentId);
  res.json({ doctors: departmentDoctors });
});

// Available slots
app.get("/api/appointments/available-slots", (req, res) => {
  const allSlots = [
    "9:00 AM - 10:00 AM", "10:00 AM - 11:00 AM", "11:00 AM - 12:00 PM",
    "12:00 PM - 1:00 PM", "1:00 PM - 2:00 PM", "2:00 PM - 3:00 PM",
    "3:00 PM - 4:00 PM", "4:00 PM - 5:00 PM"
  ];

  const availableSlots = allSlots.map(slot => ({
    timeSlot: slot,
    available: true,
    currentBookings: Math.floor(Math.random() * 2),
    maxCapacity: 4
  }));

  res.json({ availableSlots });
});

// Create appointment
app.post("/api/appointments/create", (req, res) => {
  try {
    const {
      firstName, lastName, email, mobileNumber, nic, gender,
      address, dob, department, doctor, appointmentDate, timeSlot
    } = req.body;

    const dept = departments.find(d => d._id === department);
    const doc = doctors.find(d => d._id === doctor);

    const newAppointment = {
      _id: appointmentIdCounter++,
      firstName, lastName, email, mobileNumber, nic, gender,
      address, dob, department: dept, doctor: doc,
      appointmentDate, timeSlot,
      status: 'pending',
      visited: false,
      createdAt: new Date().toISOString()
    };

    appointments.push(newAppointment);
    console.log(`✅ New appointment created for ${firstName} ${lastName}`);
    
    res.status(201).json({ 
      message: "Appointment booked successfully",
      appointment: newAppointment
    });
  } catch (error) {
    console.error("❌ Error creating appointment:", error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
});

// Simple auth middleware
const requireAuth = (req, res, next) => {
  if (!currentUser) {
    return res.status(401).json({ message: "Authentication required" });
  }
  req.user = currentUser;
  next();
};

const requireRole = (role) => {
  return (req, res, next) => {
    if (!currentUser) {
      return res.status(401).json({ message: "Authentication required" });
    }
    if (currentUser.role !== role) {
      return res.status(403).json({ message: "Insufficient permissions" });
    }
    req.user = currentUser;
    next();
  };
};

// Get all appointments (protected)
app.get("/api/appointments/all", requireAuth, (req, res) => {
  console.log(`📋 Fetching ${appointments.length} appointments for ${req.user.email}`);
  res.json({ appointments });
});

// Get appointments by user email (protected)
app.get("/api/appointments/user/:email", requireAuth, (req, res) => {
  const { email } = req.params;

  // Users can only see their own appointments, admins can see all
  if (req.user.role !== 'Admin' && req.user.email !== email) {
    return res.status(403).json({ message: "Access denied" });
  }

  const userAppointments = appointments.filter(apt => apt.email === email);
  console.log(`📋 Fetching ${userAppointments.length} appointments for user: ${email}`);
  res.json({ appointments: userAppointments });
});

// Get system statistics (admin only)
app.get("/api/system/stats", requireRole('Admin'), (req, res) => {
  const uniqueUsers = new Set(appointments.map(apt => apt.email)).size;
  const stats = {
    totalAppointments: appointments.length,
    totalUsers: uniqueUsers,
    totalDoctors: doctors.length,
    totalDepartments: departments.length,
    totalReports: patientReports.length,
    totalLeaves: doctorLeaves.length
  };
  console.log(`📊 System stats requested by admin: ${req.user.email}`);
  res.json({ stats });
});

// Basic auth endpoints
app.post("/api/auth/signup", (req, res) => {
  try {
    const { name, email, password, role } = req.body;
    console.log(`📝 Signup attempt: ${email} as ${role}`);
    console.log('Request body:', req.body);

    // Validate required fields
    if (!name || !email || !password || !role) {
      console.log('❌ Missing required fields:', { name: !!name, email: !!email, password: !!password, role: !!role });
      return res.status(400).json({ message: "All fields are required" });
    }

    // Check if user already exists
    const existingUser = users.find(u => u.email === email);
    if (existingUser) {
      console.log(`❌ User already exists: ${email}`);
      return res.status(400).json({ message: "User already exists with this email" });
    }

    // Create new user
    const newUser = {
      id: users.length + 1,
      name,
      email,
      password, // In production, this should be hashed
      role
    };

    users.push(newUser);
    console.log(`👤 User added to users array. Total users: ${users.length}`);

    // If role is Doctor, also add to doctors array
    if (role === 'Doctor') {
      try {
        console.log(`🔍 Processing doctor registration for: ${name}`);
        console.log(`📋 Current departments:`, departments);
        console.log(`👥 Current doctors count: ${doctors.length}`);

        const newDoctor = {
          _id: `doc${doctors.length + 1}`,
          firstName: name.split(' ')[0] || name,
          lastName: name.split(' ')[1] || '',
          email,
          specialization: 'General Medicine', // Default specialization
          department: departments[0]?._id || 'dept1' // Default department ID
        };

        console.log(`👨‍⚕️ Creating new doctor:`, newDoctor);
        doctors.push(newDoctor);
        console.log(`✅ Doctor added successfully. Total doctors: ${doctors.length}`);
      } catch (doctorError) {
        console.error(`❌ Error adding doctor to doctors array:`, doctorError);
        // Continue with user creation even if doctor addition fails
      }
    }

    console.log(`✅ User created successfully: ${email} as ${role}`);
    res.status(201).json({
      message: "User created successfully",
      user: { id: newUser.id, name: newUser.name, email: newUser.email, role: newUser.role }
    });
  } catch (error) {
    console.error('❌ Signup error:', error);
    res.status(500).json({ message: "Internal server error", error: error.message });
  }
});

app.post("/api/auth/login", (req, res) => {
  const { email, password } = req.body;
  console.log(`🔐 Login attempt: ${email}`);

  // Find user in users array
  const user = users.find(u => u.email === email && u.password === password);

  if (!user) {
    console.log(`❌ Login failed: Invalid credentials for ${email}`);
    return res.status(401).json({ message: "Invalid credentials" });
  }

  console.log(`✅ Login successful: ${user.email} as ${user.role}`);
  // Set current user for session
  currentUser = { id: user.id, name: user.name, email: user.email, role: user.role };
  res.json({ message: "Login successful", user: currentUser });
});

app.get("/api/auth/verify", (req, res) => {
  if (currentUser) {
    console.log(`✅ User verified: ${currentUser.email}`);
    res.json({ message: "User authenticated", user: currentUser });
  } else {
    console.log("❌ No authenticated user");
    res.status(401).json({ message: "Not authenticated" });
  }
});

app.post("/api/auth/logout", (req, res) => {
  if (currentUser) {
    console.log(`🚪 User logged out: ${currentUser.email}`);
    currentUser = null; // Clear current user
  } else {
    console.log("🚪 Logout attempt (no user was logged in)");
  }
  res.json({ message: "Logged out successfully" });
});

// Patient Reports endpoints
app.post("/api/patient-reports/create", requireRole('Doctor'), (req, res) => {
  try {
    const newReport = {
      _id: reportIdCounter++,
      ...req.body,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    patientReports.push(newReport);
    console.log(`📄 Patient report created for appointment: ${req.body.appointmentId}`);
    res.status(201).json({ message: "Report created successfully", report: newReport });
  } catch (error) {
    res.status(500).json({ message: "Error creating report", error: error.message });
  }
});

app.get("/api/patient-reports/appointment/:appointmentId", requireAuth, (req, res) => {
  const { appointmentId } = req.params;
  const report = patientReports.find(r => r.appointmentId === appointmentId);
  if (report) {
    res.json({ report });
  } else {
    res.status(404).json({ message: "Report not found" });
  }
});

app.put("/api/patient-reports/:reportId", requireRole('Doctor'), (req, res) => {
  try {
    const { reportId } = req.params;
    const reportIndex = patientReports.findIndex(r => r._id == reportId);
    if (reportIndex !== -1) {
      patientReports[reportIndex] = {
        ...patientReports[reportIndex],
        ...req.body,
        updatedAt: new Date().toISOString()
      };
      console.log(`📄 Patient report updated: ${reportId}`);
      res.json({ message: "Report updated successfully", report: patientReports[reportIndex] });
    } else {
      res.status(404).json({ message: "Report not found" });
    }
  } catch (error) {
    res.status(500).json({ message: "Error updating report", error: error.message });
  }
});

// Doctor Leaves endpoints
app.post("/api/doctor-leaves/create", requireRole('Doctor'), (req, res) => {
  try {
    const { date, reason } = req.body;
    const newLeave = {
      _id: leaveIdCounter++,
      date,
      reason: reason || 'Personal Leave',
      createdAt: new Date().toISOString()
    };
    doctorLeaves.push(newLeave);

    // Reschedule appointments on this date
    const affectedAppointments = appointments.filter(apt =>
      apt.appointmentDate.split('T')[0] === date
    );

    affectedAppointments.forEach(apt => {
      apt.status = 'rescheduled';
      apt.rescheduled = true;
      apt.originalDate = apt.appointmentDate;
      // In a real app, you'd reschedule to next available date
    });

    console.log(`🏖️ Doctor leave marked for ${date}, ${affectedAppointments.length} appointments rescheduled`);
    res.status(201).json({
      message: "Leave marked successfully",
      leave: newLeave,
      rescheduledAppointments: affectedAppointments.length
    });
  } catch (error) {
    res.status(500).json({ message: "Error marking leave", error: error.message });
  }
});

app.get("/api/doctor-leaves/all", requireRole('Doctor'), (req, res) => {
  console.log(`🏖️ Fetching ${doctorLeaves.length} leave dates`);
  res.json({ leaves: doctorLeaves });
});

// Add sample appointments
const addSampleData = () => {
  const sampleAppointments = [
    {
      _id: appointmentIdCounter++,
      firstName: "John", lastName: "Doe", email: "<EMAIL>",
      mobileNumber: "1234567890", nic: "123456789V", gender: "male",
      address: "123 Main St", dob: "1990-01-15",
      department: departments[0], doctor: doctors[0],
      appointmentDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      timeSlot: "9:00 AM - 10:00 AM", status: 'pending', visited: false,
      createdAt: new Date().toISOString()
    },
    {
      _id: appointmentIdCounter++,
      firstName: "Jane", lastName: "Smith", email: "<EMAIL>",
      mobileNumber: "0987654321", nic: "987654321V", gender: "female",
      address: "456 Oak Ave", dob: "1985-05-20",
      department: departments[1], doctor: doctors[1],
      appointmentDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(),
      timeSlot: "10:00 AM - 11:00 AM", status: 'pending', visited: false,
      createdAt: new Date().toISOString()
    }
  ];
  
  appointments.push(...sampleAppointments);
  console.log(`📝 Added ${sampleAppointments.length} sample appointments`);
};

// Start server
const PORT = 5000;
app.listen(PORT, () => {
  console.log(`🚀 Hospital Management Backend running on http://localhost:${PORT}`);
  console.log(`📊 API Status: http://localhost:${PORT}`);
  addSampleData();
}).on('error', (err) => {
  if (err.code === 'EADDRINUSE') {
    console.log(`❌ Port ${PORT} is already in use. Please close other applications using this port.`);
  } else {
    console.error('❌ Server error:', err);
  }
});
