[{"C:\\Users\\<USER>\\Desktop\\my-app\\my-app\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\my-app\\my-app\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\my-app\\my-app\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Desktop\\my-app\\my-app\\src\\pages\\Navbar.js": "4", "C:\\Users\\<USER>\\Desktop\\my-app\\my-app\\src\\pages\\Signup.js": "5", "C:\\Users\\<USER>\\Desktop\\my-app\\my-app\\src\\pages\\Login.js": "6", "C:\\Users\\<USER>\\Desktop\\my-app\\my-app\\src\\pages\\Home.js": "7", "C:\\Users\\<USER>\\Desktop\\my-app\\my-app\\src\\pages\\Appointment.js": "8", "C:\\Users\\<USER>\\Desktop\\my-app\\my-app\\src\\pages\\AboutUs.js": "9", "C:\\Users\\<USER>\\Desktop\\my-app\\my-app\\src\\pages\\Footer.js": "10", "C:\\Users\\<USER>\\Desktop\\my-app\\my-app\\src\\pages\\AppointmentsTable.js": "11", "C:\\Users\\<USER>\\Desktop\\my-app\\my-app\\src\\pages\\DoctorInformationForm.js": "12", "C:\\Users\\<USER>\\Desktop\\my-app\\my-app\\src\\pages\\AdminPage.js": "13", "C:\\Users\\<USER>\\Desktop\\my-app\\my-app\\src\\pages\\DoctorDashboard.js": "14", "C:\\Users\\<USER>\\Desktop\\my-app\\my-app\\src\\pages\\DoctorDashboard2.js": "15", "C:\\Users\\<USER>\\Desktop\\my-app\\my-app\\src\\pages\\AppointmentCalendar.js": "16", "C:\\Users\\<USER>\\Desktop\\my-app\\my-app\\src\\pages\\Manage.js": "17", "C:\\Users\\<USER>\\Desktop\\my-app\\my-app\\src\\pages\\Analytics.js": "18", "C:\\Users\\<USER>\\Desktop\\my-app\\my-app\\src\\pages\\Messages.js": "19", "C:\\Users\\<USER>\\Desktop\\my-app\\my-app\\src\\pages\\AddDepartment.js": "20", "C:\\Users\\<USER>\\Desktop\\my-app\\my-app\\src\\pages\\AdminDashboard.js": "21", "C:\\Users\\<USER>\\Desktop\\my-app\\my-app\\src\\context\\AuthContext.js": "22", "C:\\Users\\<USER>\\Desktop\\my-app\\my-app\\src\\components\\ProtectedRoute.js": "23", "C:\\Users\\<USER>\\Desktop\\my-app\\my-app\\src\\pages\\Contact.js": "24", "C:\\Users\\<USER>\\Desktop\\my-app\\my-app\\src\\pages\\UserAppointmentHistory.js": "25", "C:\\Users\\<USER>\\Desktop\\my-app\\my-app\\src\\pages\\PatientReportForm.js": "26", "C:\\Users\\<USER>\\Desktop\\my-app\\my-app\\src\\pages\\AppointmentAnalytics.js": "27"}, {"size": 535, "mtime": 1738689915829, "results": "28", "hashOfConfig": "29"}, {"size": 3988, "mtime": 1750827620988, "results": "30", "hashOfConfig": "29"}, {"size": 362, "mtime": 1738689916270, "results": "31", "hashOfConfig": "29"}, {"size": 2743, "mtime": 1750828492627, "results": "32", "hashOfConfig": "29"}, {"size": 2594, "mtime": 1750943134873, "results": "33", "hashOfConfig": "29"}, {"size": 2219, "mtime": 1750828843974, "results": "34", "hashOfConfig": "29"}, {"size": 1772, "mtime": 1750339616925, "results": "35", "hashOfConfig": "29"}, {"size": 8459, "mtime": 1750336002586, "results": "36", "hashOfConfig": "29"}, {"size": 2676, "mtime": 1739383445050, "results": "37", "hashOfConfig": "29"}, {"size": 312, "mtime": 1741783156861, "results": "38", "hashOfConfig": "29"}, {"size": 3945, "mtime": 1750824780696, "results": "39", "hashOfConfig": "29"}, {"size": 5773, "mtime": 1750945196253, "results": "40", "hashOfConfig": "29"}, {"size": 5052, "mtime": 1740567160993, "results": "41", "hashOfConfig": "29"}, {"size": 7162, "mtime": 1741885322762, "results": "42", "hashOfConfig": "29"}, {"size": 6162, "mtime": 1750827696434, "results": "43", "hashOfConfig": "29"}, {"size": 6611, "mtime": 1750824863070, "results": "44", "hashOfConfig": "29"}, {"size": 8331, "mtime": 1750337226909, "results": "45", "hashOfConfig": "29"}, {"size": 12142, "mtime": 1750828946662, "results": "46", "hashOfConfig": "29"}, {"size": 2619, "mtime": 1750333611746, "results": "47", "hashOfConfig": "29"}, {"size": 4739, "mtime": 1750335819294, "results": "48", "hashOfConfig": "29"}, {"size": 1205, "mtime": 1741782227136, "results": "49", "hashOfConfig": "29"}, {"size": 3022, "mtime": 1750943063142, "results": "50", "hashOfConfig": "29"}, {"size": 499, "mtime": 1749707877766, "results": "51", "hashOfConfig": "29"}, {"size": 3065, "mtime": 1750327302928, "results": "52", "hashOfConfig": "29"}, {"size": 8193, "mtime": 1750824667708, "results": "53", "hashOfConfig": "29"}, {"size": 8755, "mtime": 1750824703660, "results": "54", "hashOfConfig": "29"}, {"size": 7786, "mtime": 1750827600370, "results": "55", "hashOfConfig": "29"}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1xtdras", {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\my-app\\my-app\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\my-app\\my-app\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\my-app\\my-app\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\my-app\\my-app\\src\\pages\\Navbar.js", [], [], "C:\\Users\\<USER>\\Desktop\\my-app\\my-app\\src\\pages\\Signup.js", [], [], "C:\\Users\\<USER>\\Desktop\\my-app\\my-app\\src\\pages\\Login.js", [], [], "C:\\Users\\<USER>\\Desktop\\my-app\\my-app\\src\\pages\\Home.js", ["137"], [], "C:\\Users\\<USER>\\Desktop\\my-app\\my-app\\src\\pages\\Appointment.js", ["138"], [], "C:\\Users\\<USER>\\Desktop\\my-app\\my-app\\src\\pages\\AboutUs.js", [], [], "C:\\Users\\<USER>\\Desktop\\my-app\\my-app\\src\\pages\\Footer.js", ["139"], [], "C:\\Users\\<USER>\\Desktop\\my-app\\my-app\\src\\pages\\AppointmentsTable.js", [], [], "C:\\Users\\<USER>\\Desktop\\my-app\\my-app\\src\\pages\\DoctorInformationForm.js", ["140"], [], "C:\\Users\\<USER>\\Desktop\\my-app\\my-app\\src\\pages\\AdminPage.js", ["141", "142", "143"], [], "C:\\Users\\<USER>\\Desktop\\my-app\\my-app\\src\\pages\\DoctorDashboard.js", ["144"], [], "C:\\Users\\<USER>\\Desktop\\my-app\\my-app\\src\\pages\\DoctorDashboard2.js", ["145"], [], "C:\\Users\\<USER>\\Desktop\\my-app\\my-app\\src\\pages\\AppointmentCalendar.js", [], [], "C:\\Users\\<USER>\\Desktop\\my-app\\my-app\\src\\pages\\Manage.js", [], [], "C:\\Users\\<USER>\\Desktop\\my-app\\my-app\\src\\pages\\Analytics.js", ["146", "147"], [], "C:\\Users\\<USER>\\Desktop\\my-app\\my-app\\src\\pages\\Messages.js", [], [], "C:\\Users\\<USER>\\Desktop\\my-app\\my-app\\src\\pages\\AddDepartment.js", ["148"], [], "C:\\Users\\<USER>\\Desktop\\my-app\\my-app\\src\\pages\\AdminDashboard.js", ["149"], [], "C:\\Users\\<USER>\\Desktop\\my-app\\my-app\\src\\context\\AuthContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\my-app\\my-app\\src\\components\\ProtectedRoute.js", [], [], "C:\\Users\\<USER>\\Desktop\\my-app\\my-app\\src\\pages\\Contact.js", [], [], "C:\\Users\\<USER>\\Desktop\\my-app\\my-app\\src\\pages\\UserAppointmentHistory.js", ["150"], [], "C:\\Users\\<USER>\\Desktop\\my-app\\my-app\\src\\pages\\PatientReportForm.js", ["151"], [], "C:\\Users\\<USER>\\Desktop\\my-app\\my-app\\src\\pages\\AppointmentAnalytics.js", ["152"], [], {"ruleId": "153", "severity": 1, "message": "154", "line": 1, "column": 16, "nodeType": "155", "messageId": "156", "endLine": 1, "endColumn": 24}, {"ruleId": "153", "severity": 1, "message": "157", "line": 104, "column": 13, "nodeType": "155", "messageId": "156", "endLine": 104, "endColumn": 21}, {"ruleId": "153", "severity": 1, "message": "154", "line": 1, "column": 17, "nodeType": "155", "messageId": "156", "endLine": 1, "endColumn": 25}, {"ruleId": "153", "severity": 1, "message": "157", "line": 75, "column": 13, "nodeType": "155", "messageId": "156", "endLine": 75, "endColumn": 21}, {"ruleId": "153", "severity": 1, "message": "158", "line": 1, "column": 27, "nodeType": "155", "messageId": "156", "endLine": 1, "endColumn": 33}, {"ruleId": "153", "severity": 1, "message": "159", "line": 1, "column": 35, "nodeType": "155", "messageId": "156", "endLine": 1, "endColumn": 44}, {"ruleId": "153", "severity": 1, "message": "160", "line": 2, "column": 21, "nodeType": "155", "messageId": "156", "endLine": 2, "endColumn": 24}, {"ruleId": "153", "severity": 1, "message": "161", "line": 162, "column": 19, "nodeType": "155", "messageId": "156", "endLine": 162, "endColumn": 29}, {"ruleId": "153", "severity": 1, "message": "162", "line": 16, "column": 11, "nodeType": "155", "messageId": "156", "endLine": 16, "endColumn": 15}, {"ruleId": "153", "severity": 1, "message": "163", "line": 31, "column": 10, "nodeType": "155", "messageId": "156", "endLine": 31, "endColumn": 22}, {"ruleId": "164", "severity": 1, "message": "165", "line": 52, "column": 6, "nodeType": "166", "endLine": 52, "endColumn": 8, "suggestions": "167"}, {"ruleId": "153", "severity": 1, "message": "157", "line": 40, "column": 13, "nodeType": "155", "messageId": "156", "endLine": 40, "endColumn": 21}, {"ruleId": "153", "severity": 1, "message": "168", "line": 2, "column": 10, "nodeType": "155", "messageId": "156", "endLine": 2, "endColumn": 14}, {"ruleId": "164", "severity": 1, "message": "169", "line": 16, "column": 6, "nodeType": "166", "endLine": 16, "endColumn": 12, "suggestions": "170"}, {"ruleId": "164", "severity": 1, "message": "171", "line": 22, "column": 6, "nodeType": "166", "endLine": 22, "endColumn": 21, "suggestions": "172"}, {"ruleId": "164", "severity": 1, "message": "173", "line": 42, "column": 6, "nodeType": "166", "endLine": 42, "endColumn": 8, "suggestions": "174"}, "no-unused-vars", "'useState' is defined but never used.", "Identifier", "unusedVar", "'response' is assigned a value but never used.", "'useRef' is defined but never used.", "'useEffect' is defined but never used.", "'Pie' is defined but never used.", "'setReports' is assigned a value but never used.", "'user' is assigned a value but never used.", "'appointments' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchAllData'. Either include it or remove the dependency array.", "ArrayExpression", ["175"], "'Link' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchUserAppointments'. Either include it or remove the dependency array.", ["176"], "React Hook useEffect has a missing dependency: 'fetchExistingReport'. Either include it or remove the dependency array.", ["177"], "React Hook useEffect has a missing dependency: 'fetchAppointments'. Either include it or remove the dependency array.", ["178"], {"desc": "179", "fix": "180"}, {"desc": "181", "fix": "182"}, {"desc": "183", "fix": "184"}, {"desc": "185", "fix": "186"}, "Update the dependencies array to be: [fetchAllData]", {"range": "187", "text": "188"}, "Update the dependencies array to be: [fetchUserAppointments, user]", {"range": "189", "text": "190"}, "Update the dependencies array to be: [appointmentId, fetchExistingReport]", {"range": "191", "text": "192"}, "Update the dependencies array to be: [fetchAppointments]", {"range": "193", "text": "194"}, [1155, 1157], "[fetchAllData]", [478, 484], "[fetchUserAppointments, user]", [585, 600], "[appointmentId, fetchExistingReport]", [848, 850], "[fetchAppointments]"]