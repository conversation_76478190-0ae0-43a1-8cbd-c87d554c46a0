{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\my-app\\\\my-app\\\\src\\\\pages\\\\PatientReportForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport './FormStyles.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PatientReportForm = ({\n  appointmentId,\n  onClose,\n  onSave\n}) => {\n  _s();\n  const [report, setReport] = useState({\n    status: '',\n    diseaseDetails: '',\n    importantMeasures: [{\n      label: '',\n      value: ''\n    }],\n    allergies: [],\n    medications: '',\n    followUpPlan: '',\n    labTests: '',\n    dietaryAdvice: '',\n    notes: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const [isEditing, setIsEditing] = useState(false);\n  useEffect(() => {\n    fetchExistingReport();\n  }, [appointmentId]);\n  const fetchExistingReport = async () => {\n    try {\n      const response = await axios.get(`http://localhost:5000/api/patient-reports/appointment/${appointmentId}`);\n      setReport(response.data.report);\n      setIsEditing(true);\n    } catch (error) {\n      var _error$response;\n      if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 404) {\n        // No existing report, create new one\n        setIsEditing(false);\n      } else {\n        console.error('Error fetching report:', error);\n      }\n    }\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setReport(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleMeasureChange = (index, field, value) => {\n    const newMeasures = [...report.importantMeasures];\n    newMeasures[index][field] = value;\n    setReport(prev => ({\n      ...prev,\n      importantMeasures: newMeasures\n    }));\n  };\n  const addMeasure = () => {\n    setReport(prev => ({\n      ...prev,\n      importantMeasures: [...prev.importantMeasures, {\n        label: '',\n        value: ''\n      }]\n    }));\n  };\n  const removeMeasure = index => {\n    const newMeasures = report.importantMeasures.filter((_, i) => i !== index);\n    setReport(prev => ({\n      ...prev,\n      importantMeasures: newMeasures\n    }));\n  };\n  const handleAllergiesChange = e => {\n    const allergies = e.target.value.split(',').map(allergy => allergy.trim()).filter(allergy => allergy);\n    setReport(prev => ({\n      ...prev,\n      allergies\n    }));\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    try {\n      const url = isEditing ? `http://localhost:5000/api/patient-reports/${report._id}` : `http://localhost:5000/api/patient-reports/create`;\n      const method = isEditing ? 'put' : 'post';\n      const reportData = isEditing ? report : {\n        ...report,\n        appointmentId\n      };\n      const response = await axios[method](url, reportData);\n      alert(isEditing ? 'Report updated successfully!' : 'Report created successfully!');\n      onSave && onSave(response.data.report);\n      onClose && onClose();\n    } catch (error) {\n      console.error('Error saving report:', error);\n      alert('Error saving report');\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      position: 'fixed',\n      top: 0,\n      left: 0,\n      right: 0,\n      bottom: 0,\n      backgroundColor: 'rgba(0,0,0,0.8)',\n      display: 'flex',\n      justifyContent: 'center',\n      alignItems: 'center',\n      zIndex: 1000\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: '#1e1e1e',\n        padding: '2rem',\n        borderRadius: '8px',\n        maxWidth: '800px',\n        maxHeight: '90vh',\n        overflow: 'auto',\n        color: 'white',\n        width: '90%'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          color: '#BB86FC',\n          marginBottom: '1rem'\n        },\n        children: isEditing ? 'Edit Patient Report' : 'Create Patient Report'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Patient Status\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          name: \"status\",\n          value: report.status,\n          onChange: handleInputChange,\n          required: true,\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"\",\n            children: \"Select Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"Critical\",\n            children: \"Critical\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"Stable\",\n            children: \"Stable\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"Improving\",\n            children: \"Improving\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"Discharged\",\n            children: \"Discharged\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Disease Details\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n          name: \"diseaseDetails\",\n          value: report.diseaseDetails,\n          onChange: handleInputChange,\n          placeholder: \"Describe the diagnosis and condition\",\n          required: true,\n          rows: \"3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Important Measures\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this), report.importantMeasures.map((measure, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: '1rem',\n            marginBottom: '0.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Measure (e.g., Blood Pressure)\",\n            value: measure.label,\n            onChange: e => handleMeasureChange(index, 'label', e.target.value),\n            style: {\n              flex: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Value (e.g., 120/80)\",\n            value: measure.value,\n            onChange: e => handleMeasureChange(index, 'value', e.target.value),\n            style: {\n              flex: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 15\n          }, this), report.importantMeasures.length > 1 && /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: () => removeMeasure(index),\n            style: {\n              backgroundColor: '#dc3545',\n              color: 'white',\n              border: 'none',\n              padding: '0.5rem',\n              borderRadius: '4px'\n            },\n            children: \"Remove\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 17\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 13\n        }, this)), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: addMeasure,\n          style: {\n            backgroundColor: '#28a745',\n            color: 'white',\n            border: 'none',\n            padding: '0.5rem 1rem',\n            borderRadius: '4px',\n            marginBottom: '1rem'\n          },\n          children: \"Add Measure\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Allergies (comma-separated)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: \"e.g., Penicillin, Peanuts, Latex\",\n          value: report.allergies.join(', '),\n          onChange: handleAllergiesChange\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Medications\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n          name: \"medications\",\n          value: report.medications,\n          onChange: handleInputChange,\n          placeholder: \"Current medications and dosages\",\n          rows: \"2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Follow-up Plan\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n          name: \"followUpPlan\",\n          value: report.followUpPlan,\n          onChange: handleInputChange,\n          placeholder: \"Next steps and follow-up instructions\",\n          rows: \"2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Lab Tests\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n          name: \"labTests\",\n          value: report.labTests,\n          onChange: handleInputChange,\n          placeholder: \"Required lab tests and investigations\",\n          rows: \"2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Dietary Advice\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n          name: \"dietaryAdvice\",\n          value: report.dietaryAdvice,\n          onChange: handleInputChange,\n          placeholder: \"Nutrition and dietary recommendations\",\n          rows: \"2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Doctor's Notes\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n          name: \"notes\",\n          value: report.notes,\n          onChange: handleInputChange,\n          placeholder: \"Additional notes and observations\",\n          rows: \"3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: '1rem',\n            marginTop: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: loading,\n            style: {\n              backgroundColor: '#007BFF',\n              color: 'white',\n              border: 'none',\n              padding: '0.75rem 1.5rem',\n              borderRadius: '4px',\n              cursor: 'pointer',\n              flex: 1\n            },\n            children: loading ? 'Saving...' : isEditing ? 'Update Report' : 'Create Report'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: onClose,\n            style: {\n              backgroundColor: '#6c757d',\n              color: 'white',\n              border: 'none',\n              padding: '0.75rem 1.5rem',\n              borderRadius: '4px',\n              cursor: 'pointer',\n              flex: 1\n            },\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 106,\n    columnNumber: 5\n  }, this);\n};\n_s(PatientReportForm, \"VnTDvIsjBhB0jGAT9jOEkKYxrY8=\");\n_c = PatientReportForm;\nexport default PatientReportForm;\nvar _c;\n$RefreshReg$(_c, \"PatientReportForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "jsxDEV", "_jsxDEV", "PatientReportForm", "appointmentId", "onClose", "onSave", "_s", "report", "setReport", "status", "diseaseDetails", "importantMeasures", "label", "value", "allergies", "medications", "followUpPlan", "labTests", "dietaryAdvice", "notes", "loading", "setLoading", "isEditing", "setIsEditing", "fetchExistingReport", "response", "get", "data", "error", "_error$response", "console", "handleInputChange", "e", "name", "target", "prev", "handleMeasureChange", "index", "field", "newMeasures", "addMeasure", "removeMeasure", "filter", "_", "i", "handleAllergiesChange", "split", "map", "allergy", "trim", "handleSubmit", "preventDefault", "url", "_id", "method", "reportData", "alert", "style", "position", "top", "left", "right", "bottom", "backgroundColor", "display", "justifyContent", "alignItems", "zIndex", "children", "padding", "borderRadius", "max<PERSON><PERSON><PERSON>", "maxHeight", "overflow", "color", "width", "marginBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "onChange", "required", "placeholder", "rows", "measure", "gap", "type", "flex", "length", "onClick", "border", "join", "marginTop", "disabled", "cursor", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/my-app/my-app/src/pages/PatientReportForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport './FormStyles.css';\n\nconst PatientReportForm = ({ appointmentId, onClose, onSave }) => {\n  const [report, setReport] = useState({\n    status: '',\n    diseaseDetails: '',\n    importantMeasures: [{ label: '', value: '' }],\n    allergies: [],\n    medications: '',\n    followUpPlan: '',\n    labTests: '',\n    dietaryAdvice: '',\n    notes: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const [isEditing, setIsEditing] = useState(false);\n\n  useEffect(() => {\n    fetchExistingReport();\n  }, [appointmentId]);\n\n  const fetchExistingReport = async () => {\n    try {\n      const response = await axios.get(`http://localhost:5000/api/patient-reports/appointment/${appointmentId}`);\n      setReport(response.data.report);\n      setIsEditing(true);\n    } catch (error) {\n      if (error.response?.status === 404) {\n        // No existing report, create new one\n        setIsEditing(false);\n      } else {\n        console.error('Error fetching report:', error);\n      }\n    }\n  };\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setReport(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleMeasureChange = (index, field, value) => {\n    const newMeasures = [...report.importantMeasures];\n    newMeasures[index][field] = value;\n    setReport(prev => ({\n      ...prev,\n      importantMeasures: newMeasures\n    }));\n  };\n\n  const addMeasure = () => {\n    setReport(prev => ({\n      ...prev,\n      importantMeasures: [...prev.importantMeasures, { label: '', value: '' }]\n    }));\n  };\n\n  const removeMeasure = (index) => {\n    const newMeasures = report.importantMeasures.filter((_, i) => i !== index);\n    setReport(prev => ({\n      ...prev,\n      importantMeasures: newMeasures\n    }));\n  };\n\n  const handleAllergiesChange = (e) => {\n    const allergies = e.target.value.split(',').map(allergy => allergy.trim()).filter(allergy => allergy);\n    setReport(prev => ({\n      ...prev,\n      allergies\n    }));\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n\n    try {\n      const url = isEditing \n        ? `http://localhost:5000/api/patient-reports/${report._id}`\n        : `http://localhost:5000/api/patient-reports/create`;\n      \n      const method = isEditing ? 'put' : 'post';\n      \n      const reportData = isEditing ? report : { ...report, appointmentId };\n\n      const response = await axios[method](url, reportData);\n\n      alert(isEditing ? 'Report updated successfully!' : 'Report created successfully!');\n      onSave && onSave(response.data.report);\n      onClose && onClose();\n    } catch (error) {\n      console.error('Error saving report:', error);\n      alert('Error saving report');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div style={{\n      position: 'fixed',\n      top: 0,\n      left: 0,\n      right: 0,\n      bottom: 0,\n      backgroundColor: 'rgba(0,0,0,0.8)',\n      display: 'flex',\n      justifyContent: 'center',\n      alignItems: 'center',\n      zIndex: 1000\n    }}>\n      <div style={{\n        backgroundColor: '#1e1e1e',\n        padding: '2rem',\n        borderRadius: '8px',\n        maxWidth: '800px',\n        maxHeight: '90vh',\n        overflow: 'auto',\n        color: 'white',\n        width: '90%'\n      }}>\n        <h2 style={{ color: '#BB86FC', marginBottom: '1rem' }}>\n          {isEditing ? 'Edit Patient Report' : 'Create Patient Report'}\n        </h2>\n\n        <form onSubmit={handleSubmit}>\n          <label>Patient Status</label>\n          <select\n            name=\"status\"\n            value={report.status}\n            onChange={handleInputChange}\n            required\n          >\n            <option value=\"\">Select Status</option>\n            <option value=\"Critical\">Critical</option>\n            <option value=\"Stable\">Stable</option>\n            <option value=\"Improving\">Improving</option>\n            <option value=\"Discharged\">Discharged</option>\n          </select>\n\n          <label>Disease Details</label>\n          <textarea\n            name=\"diseaseDetails\"\n            value={report.diseaseDetails}\n            onChange={handleInputChange}\n            placeholder=\"Describe the diagnosis and condition\"\n            required\n            rows=\"3\"\n          />\n\n          <label>Important Measures</label>\n          {report.importantMeasures.map((measure, index) => (\n            <div key={index} style={{ display: 'flex', gap: '1rem', marginBottom: '0.5rem' }}>\n              <input\n                type=\"text\"\n                placeholder=\"Measure (e.g., Blood Pressure)\"\n                value={measure.label}\n                onChange={(e) => handleMeasureChange(index, 'label', e.target.value)}\n                style={{ flex: 1 }}\n              />\n              <input\n                type=\"text\"\n                placeholder=\"Value (e.g., 120/80)\"\n                value={measure.value}\n                onChange={(e) => handleMeasureChange(index, 'value', e.target.value)}\n                style={{ flex: 1 }}\n              />\n              {report.importantMeasures.length > 1 && (\n                <button\n                  type=\"button\"\n                  onClick={() => removeMeasure(index)}\n                  style={{\n                    backgroundColor: '#dc3545',\n                    color: 'white',\n                    border: 'none',\n                    padding: '0.5rem',\n                    borderRadius: '4px'\n                  }}\n                >\n                  Remove\n                </button>\n              )}\n            </div>\n          ))}\n          <button\n            type=\"button\"\n            onClick={addMeasure}\n            style={{\n              backgroundColor: '#28a745',\n              color: 'white',\n              border: 'none',\n              padding: '0.5rem 1rem',\n              borderRadius: '4px',\n              marginBottom: '1rem'\n            }}\n          >\n            Add Measure\n          </button>\n\n          <label>Allergies (comma-separated)</label>\n          <input\n            type=\"text\"\n            placeholder=\"e.g., Penicillin, Peanuts, Latex\"\n            value={report.allergies.join(', ')}\n            onChange={handleAllergiesChange}\n          />\n\n          <label>Medications</label>\n          <textarea\n            name=\"medications\"\n            value={report.medications}\n            onChange={handleInputChange}\n            placeholder=\"Current medications and dosages\"\n            rows=\"2\"\n          />\n\n          <label>Follow-up Plan</label>\n          <textarea\n            name=\"followUpPlan\"\n            value={report.followUpPlan}\n            onChange={handleInputChange}\n            placeholder=\"Next steps and follow-up instructions\"\n            rows=\"2\"\n          />\n\n          <label>Lab Tests</label>\n          <textarea\n            name=\"labTests\"\n            value={report.labTests}\n            onChange={handleInputChange}\n            placeholder=\"Required lab tests and investigations\"\n            rows=\"2\"\n          />\n\n          <label>Dietary Advice</label>\n          <textarea\n            name=\"dietaryAdvice\"\n            value={report.dietaryAdvice}\n            onChange={handleInputChange}\n            placeholder=\"Nutrition and dietary recommendations\"\n            rows=\"2\"\n          />\n\n          <label>Doctor's Notes</label>\n          <textarea\n            name=\"notes\"\n            value={report.notes}\n            onChange={handleInputChange}\n            placeholder=\"Additional notes and observations\"\n            rows=\"3\"\n          />\n\n          <div style={{ display: 'flex', gap: '1rem', marginTop: '1rem' }}>\n            <button\n              type=\"submit\"\n              disabled={loading}\n              style={{\n                backgroundColor: '#007BFF',\n                color: 'white',\n                border: 'none',\n                padding: '0.75rem 1.5rem',\n                borderRadius: '4px',\n                cursor: 'pointer',\n                flex: 1\n              }}\n            >\n              {loading ? 'Saving...' : (isEditing ? 'Update Report' : 'Create Report')}\n            </button>\n            <button\n              type=\"button\"\n              onClick={onClose}\n              style={{\n                backgroundColor: '#6c757d',\n                color: 'white',\n                border: 'none',\n                padding: '0.75rem 1.5rem',\n                borderRadius: '4px',\n                cursor: 'pointer',\n                flex: 1\n              }}\n            >\n              Cancel\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n};\n\nexport default PatientReportForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,iBAAiB,GAAGA,CAAC;EAAEC,aAAa;EAAEC,OAAO;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EAChE,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGX,QAAQ,CAAC;IACnCY,MAAM,EAAE,EAAE;IACVC,cAAc,EAAE,EAAE;IAClBC,iBAAiB,EAAE,CAAC;MAAEC,KAAK,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAG,CAAC,CAAC;IAC7CC,SAAS,EAAE,EAAE;IACbC,WAAW,EAAE,EAAE;IACfC,YAAY,EAAE,EAAE;IAChBC,QAAQ,EAAE,EAAE;IACZC,aAAa,EAAE,EAAE;IACjBC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACyB,SAAS,EAAEC,YAAY,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAEjDC,SAAS,CAAC,MAAM;IACd0B,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,CAACrB,aAAa,CAAC,CAAC;EAEnB,MAAMqB,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAM1B,KAAK,CAAC2B,GAAG,CAAC,yDAAyDvB,aAAa,EAAE,CAAC;MAC1GK,SAAS,CAACiB,QAAQ,CAACE,IAAI,CAACpB,MAAM,CAAC;MAC/BgB,YAAY,CAAC,IAAI,CAAC;IACpB,CAAC,CAAC,OAAOK,KAAK,EAAE;MAAA,IAAAC,eAAA;MACd,IAAI,EAAAA,eAAA,GAAAD,KAAK,CAACH,QAAQ,cAAAI,eAAA,uBAAdA,eAAA,CAAgBpB,MAAM,MAAK,GAAG,EAAE;QAClC;QACAc,YAAY,CAAC,KAAK,CAAC;MACrB,CAAC,MAAM;QACLO,OAAO,CAACF,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAChD;IACF;EACF,CAAC;EAED,MAAMG,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEpB;IAAM,CAAC,GAAGmB,CAAC,CAACE,MAAM;IAChC1B,SAAS,CAAC2B,IAAI,KAAK;MACjB,GAAGA,IAAI;MACP,CAACF,IAAI,GAAGpB;IACV,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMuB,mBAAmB,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEzB,KAAK,KAAK;IACnD,MAAM0B,WAAW,GAAG,CAAC,GAAGhC,MAAM,CAACI,iBAAiB,CAAC;IACjD4B,WAAW,CAACF,KAAK,CAAC,CAACC,KAAK,CAAC,GAAGzB,KAAK;IACjCL,SAAS,CAAC2B,IAAI,KAAK;MACjB,GAAGA,IAAI;MACPxB,iBAAiB,EAAE4B;IACrB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvBhC,SAAS,CAAC2B,IAAI,KAAK;MACjB,GAAGA,IAAI;MACPxB,iBAAiB,EAAE,CAAC,GAAGwB,IAAI,CAACxB,iBAAiB,EAAE;QAAEC,KAAK,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAG,CAAC;IACzE,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAM4B,aAAa,GAAIJ,KAAK,IAAK;IAC/B,MAAME,WAAW,GAAGhC,MAAM,CAACI,iBAAiB,CAAC+B,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKP,KAAK,CAAC;IAC1E7B,SAAS,CAAC2B,IAAI,KAAK;MACjB,GAAGA,IAAI;MACPxB,iBAAiB,EAAE4B;IACrB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMM,qBAAqB,GAAIb,CAAC,IAAK;IACnC,MAAMlB,SAAS,GAAGkB,CAAC,CAACE,MAAM,CAACrB,KAAK,CAACiC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,OAAO,IAAIA,OAAO,CAACC,IAAI,CAAC,CAAC,CAAC,CAACP,MAAM,CAACM,OAAO,IAAIA,OAAO,CAAC;IACrGxC,SAAS,CAAC2B,IAAI,KAAK;MACjB,GAAGA,IAAI;MACPrB;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMoC,YAAY,GAAG,MAAOlB,CAAC,IAAK;IAChCA,CAAC,CAACmB,cAAc,CAAC,CAAC;IAClB9B,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,MAAM+B,GAAG,GAAG9B,SAAS,GACjB,6CAA6Cf,MAAM,CAAC8C,GAAG,EAAE,GACzD,kDAAkD;MAEtD,MAAMC,MAAM,GAAGhC,SAAS,GAAG,KAAK,GAAG,MAAM;MAEzC,MAAMiC,UAAU,GAAGjC,SAAS,GAAGf,MAAM,GAAG;QAAE,GAAGA,MAAM;QAAEJ;MAAc,CAAC;MAEpE,MAAMsB,QAAQ,GAAG,MAAM1B,KAAK,CAACuD,MAAM,CAAC,CAACF,GAAG,EAAEG,UAAU,CAAC;MAErDC,KAAK,CAAClC,SAAS,GAAG,8BAA8B,GAAG,8BAA8B,CAAC;MAClFjB,MAAM,IAAIA,MAAM,CAACoB,QAAQ,CAACE,IAAI,CAACpB,MAAM,CAAC;MACtCH,OAAO,IAAIA,OAAO,CAAC,CAAC;IACtB,CAAC,CAAC,OAAOwB,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C4B,KAAK,CAAC,qBAAqB,CAAC;IAC9B,CAAC,SAAS;MACRnC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEpB,OAAA;IAAKwD,KAAK,EAAE;MACVC,QAAQ,EAAE,OAAO;MACjBC,GAAG,EAAE,CAAC;MACNC,IAAI,EAAE,CAAC;MACPC,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CAAC;MACTC,eAAe,EAAE,iBAAiB;MAClCC,OAAO,EAAE,MAAM;MACfC,cAAc,EAAE,QAAQ;MACxBC,UAAU,EAAE,QAAQ;MACpBC,MAAM,EAAE;IACV,CAAE;IAAAC,QAAA,eACAnE,OAAA;MAAKwD,KAAK,EAAE;QACVM,eAAe,EAAE,SAAS;QAC1BM,OAAO,EAAE,MAAM;QACfC,YAAY,EAAE,KAAK;QACnBC,QAAQ,EAAE,OAAO;QACjBC,SAAS,EAAE,MAAM;QACjBC,QAAQ,EAAE,MAAM;QAChBC,KAAK,EAAE,OAAO;QACdC,KAAK,EAAE;MACT,CAAE;MAAAP,QAAA,gBACAnE,OAAA;QAAIwD,KAAK,EAAE;UAAEiB,KAAK,EAAE,SAAS;UAAEE,YAAY,EAAE;QAAO,CAAE;QAAAR,QAAA,EACnD9C,SAAS,GAAG,qBAAqB,GAAG;MAAuB;QAAAuD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC,eAEL/E,OAAA;QAAMgF,QAAQ,EAAE/B,YAAa;QAAAkB,QAAA,gBAC3BnE,OAAA;UAAAmE,QAAA,EAAO;QAAc;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC7B/E,OAAA;UACEgC,IAAI,EAAC,QAAQ;UACbpB,KAAK,EAAEN,MAAM,CAACE,MAAO;UACrByE,QAAQ,EAAEnD,iBAAkB;UAC5BoD,QAAQ;UAAAf,QAAA,gBAERnE,OAAA;YAAQY,KAAK,EAAC,EAAE;YAAAuD,QAAA,EAAC;UAAa;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACvC/E,OAAA;YAAQY,KAAK,EAAC,UAAU;YAAAuD,QAAA,EAAC;UAAQ;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC1C/E,OAAA;YAAQY,KAAK,EAAC,QAAQ;YAAAuD,QAAA,EAAC;UAAM;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtC/E,OAAA;YAAQY,KAAK,EAAC,WAAW;YAAAuD,QAAA,EAAC;UAAS;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC5C/E,OAAA;YAAQY,KAAK,EAAC,YAAY;YAAAuD,QAAA,EAAC;UAAU;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC,eAET/E,OAAA;UAAAmE,QAAA,EAAO;QAAe;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC9B/E,OAAA;UACEgC,IAAI,EAAC,gBAAgB;UACrBpB,KAAK,EAAEN,MAAM,CAACG,cAAe;UAC7BwE,QAAQ,EAAEnD,iBAAkB;UAC5BqD,WAAW,EAAC,sCAAsC;UAClDD,QAAQ;UACRE,IAAI,EAAC;QAAG;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAEF/E,OAAA;UAAAmE,QAAA,EAAO;QAAkB;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EAChCzE,MAAM,CAACI,iBAAiB,CAACoC,GAAG,CAAC,CAACuC,OAAO,EAAEjD,KAAK,kBAC3CpC,OAAA;UAAiBwD,KAAK,EAAE;YAAEO,OAAO,EAAE,MAAM;YAAEuB,GAAG,EAAE,MAAM;YAAEX,YAAY,EAAE;UAAS,CAAE;UAAAR,QAAA,gBAC/EnE,OAAA;YACEuF,IAAI,EAAC,MAAM;YACXJ,WAAW,EAAC,gCAAgC;YAC5CvE,KAAK,EAAEyE,OAAO,CAAC1E,KAAM;YACrBsE,QAAQ,EAAGlD,CAAC,IAAKI,mBAAmB,CAACC,KAAK,EAAE,OAAO,EAAEL,CAAC,CAACE,MAAM,CAACrB,KAAK,CAAE;YACrE4C,KAAK,EAAE;cAAEgC,IAAI,EAAE;YAAE;UAAE;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,eACF/E,OAAA;YACEuF,IAAI,EAAC,MAAM;YACXJ,WAAW,EAAC,sBAAsB;YAClCvE,KAAK,EAAEyE,OAAO,CAACzE,KAAM;YACrBqE,QAAQ,EAAGlD,CAAC,IAAKI,mBAAmB,CAACC,KAAK,EAAE,OAAO,EAAEL,CAAC,CAACE,MAAM,CAACrB,KAAK,CAAE;YACrE4C,KAAK,EAAE;cAAEgC,IAAI,EAAE;YAAE;UAAE;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,EACDzE,MAAM,CAACI,iBAAiB,CAAC+E,MAAM,GAAG,CAAC,iBAClCzF,OAAA;YACEuF,IAAI,EAAC,QAAQ;YACbG,OAAO,EAAEA,CAAA,KAAMlD,aAAa,CAACJ,KAAK,CAAE;YACpCoB,KAAK,EAAE;cACLM,eAAe,EAAE,SAAS;cAC1BW,KAAK,EAAE,OAAO;cACdkB,MAAM,EAAE,MAAM;cACdvB,OAAO,EAAE,QAAQ;cACjBC,YAAY,EAAE;YAChB,CAAE;YAAAF,QAAA,EACH;UAED;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA,GA7BO3C,KAAK;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA8BV,CACN,CAAC,eACF/E,OAAA;UACEuF,IAAI,EAAC,QAAQ;UACbG,OAAO,EAAEnD,UAAW;UACpBiB,KAAK,EAAE;YACLM,eAAe,EAAE,SAAS;YAC1BW,KAAK,EAAE,OAAO;YACdkB,MAAM,EAAE,MAAM;YACdvB,OAAO,EAAE,aAAa;YACtBC,YAAY,EAAE,KAAK;YACnBM,YAAY,EAAE;UAChB,CAAE;UAAAR,QAAA,EACH;QAED;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAET/E,OAAA;UAAAmE,QAAA,EAAO;QAA2B;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC1C/E,OAAA;UACEuF,IAAI,EAAC,MAAM;UACXJ,WAAW,EAAC,kCAAkC;UAC9CvE,KAAK,EAAEN,MAAM,CAACO,SAAS,CAAC+E,IAAI,CAAC,IAAI,CAAE;UACnCX,QAAQ,EAAErC;QAAsB;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eAEF/E,OAAA;UAAAmE,QAAA,EAAO;QAAW;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC1B/E,OAAA;UACEgC,IAAI,EAAC,aAAa;UAClBpB,KAAK,EAAEN,MAAM,CAACQ,WAAY;UAC1BmE,QAAQ,EAAEnD,iBAAkB;UAC5BqD,WAAW,EAAC,iCAAiC;UAC7CC,IAAI,EAAC;QAAG;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAEF/E,OAAA;UAAAmE,QAAA,EAAO;QAAc;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC7B/E,OAAA;UACEgC,IAAI,EAAC,cAAc;UACnBpB,KAAK,EAAEN,MAAM,CAACS,YAAa;UAC3BkE,QAAQ,EAAEnD,iBAAkB;UAC5BqD,WAAW,EAAC,uCAAuC;UACnDC,IAAI,EAAC;QAAG;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAEF/E,OAAA;UAAAmE,QAAA,EAAO;QAAS;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACxB/E,OAAA;UACEgC,IAAI,EAAC,UAAU;UACfpB,KAAK,EAAEN,MAAM,CAACU,QAAS;UACvBiE,QAAQ,EAAEnD,iBAAkB;UAC5BqD,WAAW,EAAC,uCAAuC;UACnDC,IAAI,EAAC;QAAG;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAEF/E,OAAA;UAAAmE,QAAA,EAAO;QAAc;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC7B/E,OAAA;UACEgC,IAAI,EAAC,eAAe;UACpBpB,KAAK,EAAEN,MAAM,CAACW,aAAc;UAC5BgE,QAAQ,EAAEnD,iBAAkB;UAC5BqD,WAAW,EAAC,uCAAuC;UACnDC,IAAI,EAAC;QAAG;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAEF/E,OAAA;UAAAmE,QAAA,EAAO;QAAc;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC7B/E,OAAA;UACEgC,IAAI,EAAC,OAAO;UACZpB,KAAK,EAAEN,MAAM,CAACY,KAAM;UACpB+D,QAAQ,EAAEnD,iBAAkB;UAC5BqD,WAAW,EAAC,mCAAmC;UAC/CC,IAAI,EAAC;QAAG;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAEF/E,OAAA;UAAKwD,KAAK,EAAE;YAAEO,OAAO,EAAE,MAAM;YAAEuB,GAAG,EAAE,MAAM;YAAEO,SAAS,EAAE;UAAO,CAAE;UAAA1B,QAAA,gBAC9DnE,OAAA;YACEuF,IAAI,EAAC,QAAQ;YACbO,QAAQ,EAAE3E,OAAQ;YAClBqC,KAAK,EAAE;cACLM,eAAe,EAAE,SAAS;cAC1BW,KAAK,EAAE,OAAO;cACdkB,MAAM,EAAE,MAAM;cACdvB,OAAO,EAAE,gBAAgB;cACzBC,YAAY,EAAE,KAAK;cACnB0B,MAAM,EAAE,SAAS;cACjBP,IAAI,EAAE;YACR,CAAE;YAAArB,QAAA,EAEDhD,OAAO,GAAG,WAAW,GAAIE,SAAS,GAAG,eAAe,GAAG;UAAgB;YAAAuD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClE,CAAC,eACT/E,OAAA;YACEuF,IAAI,EAAC,QAAQ;YACbG,OAAO,EAAEvF,OAAQ;YACjBqD,KAAK,EAAE;cACLM,eAAe,EAAE,SAAS;cAC1BW,KAAK,EAAE,OAAO;cACdkB,MAAM,EAAE,MAAM;cACdvB,OAAO,EAAE,gBAAgB;cACzBC,YAAY,EAAE,KAAK;cACnB0B,MAAM,EAAE,SAAS;cACjBP,IAAI,EAAE;YACR,CAAE;YAAArB,QAAA,EACH;UAED;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC1E,EAAA,CAlSIJ,iBAAiB;AAAA+F,EAAA,GAAjB/F,iBAAiB;AAoSvB,eAAeA,iBAAiB;AAAC,IAAA+F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}