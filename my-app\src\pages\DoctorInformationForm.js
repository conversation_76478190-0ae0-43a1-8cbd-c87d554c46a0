import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useAuth } from '../context/AuthContext';
import './FormStyles.css';

const DoctorInformationForm = () => {
  const { user } = useAuth();

  const [doctor, setDoctor] = useState({
    firstName: '',
    lastName: '',
    email: '',
    specialization: '',
    department: '',
    experience: '',
    qualification: '',
    image: null,
    licenseNumber: '',
    medicalCertificate: null,
  });

  const [departments, setDepartments] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    fetchDepartments();

    // Auto-fill user information if available
    if (user) {
      const nameParts = user.name ? user.name.split(' ') : ['', ''];
      setDoctor(prev => ({
        ...prev,
        firstName: nameParts[0] || '',
        lastName: nameParts.slice(1).join(' ') || '',
        email: user.email || ''
      }));
    }
  }, [user]);

  const fetchDepartments = async () => {
    try {
      const response = await axios.get('http://localhost:5000/api/departments/all');
      setDepartments(response.data.departments);
    } catch (error) {
      console.error('Error fetching departments:', error);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setDoctor((prevDoctor) => ({
      ...prevDoctor,
      [name]: value,
    }));
  };

  const handleFileChange = (e) => {
    const { name, files } = e.target;
    setDoctor((prevDoctor) => ({
      ...prevDoctor,
      [name]: files[0],
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!doctor.department) {
      alert('Please select a department');
      return;
    }

    try {
      setLoading(true);
      const response = await axios.post('http://localhost:5000/api/doctors/create', doctor, {
        withCredentials: true
      });

      alert('Doctor registration successful!');

      // Reset form
      setDoctor({
        firstName: '',
        lastName: '',
        email: '',
        specialization: '',
        department: '',
        experience: '',
        qualification: '',
        image: null,
        licenseNumber: '',
        medicalCertificate: null,
      });
    } catch (error) {
      console.error('Error registering doctor:', error);
      alert(error.response?.data?.message || 'Error registering doctor');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ padding: '2rem', maxWidth: '600px', margin: '0 auto' }}>
      <h2 style={{ color: 'white', textAlign: 'center', marginBottom: '2rem' }}>
        Doctor Registration
      </h2>

      <form onSubmit={handleSubmit}>
        <label>First Name</label>
        <input
          type="text"
          name="firstName"
          placeholder="First Name"
          value={doctor.firstName}
          onChange={handleChange}
          required
        />

        <label>Last Name</label>
        <input
          type="text"
          name="lastName"
          placeholder="Last Name"
          value={doctor.lastName}
          onChange={handleChange}
          required
        />

        <label>Email (Auto-filled from your account)</label>
        <input
          type="email"
          name="email"
          placeholder="Email Address"
          value={doctor.email}
          readOnly
          style={{ backgroundColor: '#f5f5f5', cursor: 'not-allowed' }}
          required
        />

        <label>Department</label>
        <select
          name="department"
          value={doctor.department}
          onChange={handleChange}
          required
        >
          <option value="">Select Department</option>
          {departments.map((dept) => (
            <option key={dept._id} value={dept._id}>
              {dept.name}
            </option>
          ))}
        </select>
        <label>Specialization</label>
        <input
          type="text"
          name="specialization"
          placeholder="Specialization"
          value={doctor.specialization}
          onChange={handleChange}
          required
        />

        <label>How many years of experience do you have?</label>
        <input
          type="number"
          name="experience"
          placeholder="Experience (years)"
          value={doctor.experience}
          onChange={handleChange}
          required
        />

        <label>What is your qualification?</label>
        <input
          type="text"
          name="qualification"
          placeholder="Qualification"
          value={doctor.qualification}
          onChange={handleChange}
          required
        />

        <label>Upload image for profile picture</label>
        <input
          type="file"
          name="image"
          onChange={handleFileChange}
          accept="image/*"
        />

        <label>License Number</label>
        <input
          type="text"
          name="licenseNumber"
          placeholder="License Number"
          value={doctor.licenseNumber}
          onChange={handleChange}
          required
        />

        <label>Upload medical registration certificate</label>
        <input
          type="file"
          name="medicalCertificate"
          onChange={handleFileChange}
          accept=".pdf,.doc,.docx"
        />

        <button type="submit" disabled={loading}>
          {loading ? 'Registering...' : 'Submit Registration'}
        </button>
      </form>
    </div>
  );
};

export default DoctorInformationForm;
