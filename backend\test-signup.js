const axios = require('axios');

async function testSignup() {
  try {
    console.log('Testing doctor signup...');
    
    const doctorData = {
      name: 'Test Doctor',
      email: '<EMAIL>',
      password: 'password123',
      role: 'Doctor'
    };
    
    console.log('Sending signup request:', doctorData);
    
    const response = await axios.post('http://localhost:5000/api/auth/signup', doctorData);
    
    console.log('✅ Signup successful!');
    console.log('Response:', response.data);
    
    // Test getting debug state
    const debugResponse = await axios.get('http://localhost:5000/api/debug/state');
    console.log('📊 Current state:', debugResponse.data);
    
  } catch (error) {
    console.error('❌ Signup failed!');
    console.error('Error:', error.response?.data || error.message);
  }
}

async function testUserSignup() {
  try {
    console.log('Testing user signup...');
    
    const userData = {
      name: 'Test User',
      email: '<EMAIL>',
      password: 'password123',
      role: 'User'
    };
    
    console.log('Sending signup request:', userData);
    
    const response = await axios.post('http://localhost:5000/api/auth/signup', userData);
    
    console.log('✅ User signup successful!');
    console.log('Response:', response.data);
    
  } catch (error) {
    console.error('❌ User signup failed!');
    console.error('Error:', error.response?.data || error.message);
  }
}

// Run tests
testUserSignup().then(() => testSignup());
