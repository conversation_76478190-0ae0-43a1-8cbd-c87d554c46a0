{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\my-app\\\\my-app\\\\src\\\\context\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [sessionId, setSessionId] = useState(localStorage.getItem('sessionId'));\n\n  // Check if user is authenticated on app load\n  useEffect(() => {\n    checkAuthStatus();\n  }, []);\n  const checkAuthStatus = async () => {\n    try {\n      const storedSessionId = localStorage.getItem('sessionId');\n      if (!storedSessionId) {\n        setUser(null);\n        setLoading(false);\n        return;\n      }\n      const response = await axios.get('http://localhost:5000/api/auth/verify', {\n        headers: {\n          'x-session-id': storedSessionId\n        },\n        withCredentials: true\n      });\n      setUser(response.data.user);\n      setSessionId(storedSessionId);\n    } catch (error) {\n      console.log('User not authenticated');\n      setUser(null);\n      setSessionId(null);\n      localStorage.removeItem('sessionId');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const login = async (email, password) => {\n    try {\n      const response = await axios.post('http://localhost:5000/api/auth/login', {\n        email,\n        password\n      }, {\n        withCredentials: true\n      });\n\n      // Set user and session from login response\n      const {\n        user,\n        sessionId\n      } = response.data;\n      setUser(user);\n      setSessionId(sessionId);\n      localStorage.setItem('sessionId', sessionId);\n      return {\n        success: true,\n        message: response.data.message,\n        user: user\n      };\n    } catch (error) {\n      var _error$response, _error$response$data;\n      return {\n        success: false,\n        message: ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Login failed'\n      };\n    }\n  };\n  const signup = async userData => {\n    try {\n      console.log('Sending signup request with data:', userData);\n      const response = await axios.post('http://localhost:5000/api/auth/signup', userData, {\n        withCredentials: true\n      });\n      console.log('Signup response:', response.data);\n      return {\n        success: true,\n        message: response.data.message\n      };\n    } catch (error) {\n      var _error$response2, _error$response3, _error$response3$data;\n      console.error('Signup error:', error);\n      console.error('Error response:', (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.data);\n      return {\n        success: false,\n        message: ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.message) || error.message || 'Signup failed'\n      };\n    }\n  };\n  const logout = async () => {\n    try {\n      const storedSessionId = localStorage.getItem('sessionId');\n      if (storedSessionId) {\n        await axios.post('http://localhost:5000/api/auth/logout', {}, {\n          headers: {\n            'x-session-id': storedSessionId\n          },\n          withCredentials: true\n        });\n      }\n      setUser(null);\n      setSessionId(null);\n      localStorage.removeItem('sessionId');\n      localStorage.removeItem('user');\n      sessionStorage.removeItem('user');\n      return {\n        success: true,\n        message: 'Logged out successfully'\n      };\n    } catch (error) {\n      // Even if logout request fails, clear local state\n      setUser(null);\n      setSessionId(null);\n      localStorage.removeItem('sessionId');\n      localStorage.removeItem('user');\n      sessionStorage.removeItem('user');\n      return {\n        success: true,\n        // Return success to ensure UI updates\n        message: 'Logged out successfully'\n      };\n    }\n  };\n\n  // Set up axios interceptor to automatically add session header\n  useEffect(() => {\n    const interceptor = axios.interceptors.request.use(config => {\n      const storedSessionId = localStorage.getItem('sessionId');\n      if (storedSessionId) {\n        config.headers['x-session-id'] = storedSessionId;\n      }\n      return config;\n    });\n    return () => {\n      axios.interceptors.request.eject(interceptor);\n    };\n  }, []);\n  const value = {\n    user,\n    login,\n    signup,\n    logout,\n    loading,\n    checkAuthStatus,\n    sessionId\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 155,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"u6ads01v7HGQbFQnImROHhgbkEY=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "axios", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "loading", "setLoading", "sessionId", "setSessionId", "localStorage", "getItem", "checkAuthStatus", "storedSessionId", "response", "get", "headers", "withCredentials", "data", "error", "console", "log", "removeItem", "login", "email", "password", "post", "setItem", "success", "message", "_error$response", "_error$response$data", "signup", "userData", "_error$response2", "_error$response3", "_error$response3$data", "logout", "sessionStorage", "interceptor", "interceptors", "request", "use", "config", "eject", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/my-app/my-app/src/context/AuthContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\nimport axios from 'axios';\n\nconst AuthContext = createContext();\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [sessionId, setSessionId] = useState(localStorage.getItem('sessionId'));\n\n  // Check if user is authenticated on app load\n  useEffect(() => {\n    checkAuthStatus();\n  }, []);\n\n  const checkAuthStatus = async () => {\n    try {\n      const storedSessionId = localStorage.getItem('sessionId');\n      if (!storedSessionId) {\n        setUser(null);\n        setLoading(false);\n        return;\n      }\n\n      const response = await axios.get('http://localhost:5000/api/auth/verify', {\n        headers: {\n          'x-session-id': storedSessionId\n        },\n        withCredentials: true\n      });\n      setUser(response.data.user);\n      setSessionId(storedSessionId);\n    } catch (error) {\n      console.log('User not authenticated');\n      setUser(null);\n      setSessionId(null);\n      localStorage.removeItem('sessionId');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const login = async (email, password) => {\n    try {\n      const response = await axios.post('http://localhost:5000/api/auth/login', {\n        email,\n        password\n      }, {\n        withCredentials: true\n      });\n\n      // Set user and session from login response\n      const { user, sessionId } = response.data;\n      setUser(user);\n      setSessionId(sessionId);\n      localStorage.setItem('sessionId', sessionId);\n\n      return {\n        success: true,\n        message: response.data.message,\n        user: user\n      };\n    } catch (error) {\n      return {\n        success: false,\n        message: error.response?.data?.message || 'Login failed'\n      };\n    }\n  };\n\n  const signup = async (userData) => {\n    try {\n      console.log('Sending signup request with data:', userData);\n      const response = await axios.post('http://localhost:5000/api/auth/signup', userData, {\n        withCredentials: true\n      });\n      console.log('Signup response:', response.data);\n      return { success: true, message: response.data.message };\n    } catch (error) {\n      console.error('Signup error:', error);\n      console.error('Error response:', error.response?.data);\n      return {\n        success: false,\n        message: error.response?.data?.message || error.message || 'Signup failed'\n      };\n    }\n  };\n\n  const logout = async () => {\n    try {\n      const storedSessionId = localStorage.getItem('sessionId');\n      if (storedSessionId) {\n        await axios.post('http://localhost:5000/api/auth/logout', {}, {\n          headers: {\n            'x-session-id': storedSessionId\n          },\n          withCredentials: true\n        });\n      }\n\n      setUser(null);\n      setSessionId(null);\n      localStorage.removeItem('sessionId');\n      localStorage.removeItem('user');\n      sessionStorage.removeItem('user');\n      return { success: true, message: 'Logged out successfully' };\n    } catch (error) {\n      // Even if logout request fails, clear local state\n      setUser(null);\n      setSessionId(null);\n      localStorage.removeItem('sessionId');\n      localStorage.removeItem('user');\n      sessionStorage.removeItem('user');\n      return {\n        success: true, // Return success to ensure UI updates\n        message: 'Logged out successfully'\n      };\n    }\n  };\n\n  // Set up axios interceptor to automatically add session header\n  useEffect(() => {\n    const interceptor = axios.interceptors.request.use((config) => {\n      const storedSessionId = localStorage.getItem('sessionId');\n      if (storedSessionId) {\n        config.headers['x-session-id'] = storedSessionId;\n      }\n      return config;\n    });\n\n    return () => {\n      axios.interceptors.request.eject(interceptor);\n    };\n  }, []);\n\n  const value = {\n    user,\n    login,\n    signup,\n    logout,\n    loading,\n    checkAuthStatus,\n    sessionId\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,WAAW,gBAAGP,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMQ,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGT,UAAU,CAACM,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACiB,SAAS,EAAEC,YAAY,CAAC,GAAGlB,QAAQ,CAACmB,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,CAAC;;EAE7E;EACAnB,SAAS,CAAC,MAAM;IACdoB,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMC,eAAe,GAAGH,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;MACzD,IAAI,CAACE,eAAe,EAAE;QACpBR,OAAO,CAAC,IAAI,CAAC;QACbE,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;MAEA,MAAMO,QAAQ,GAAG,MAAMrB,KAAK,CAACsB,GAAG,CAAC,uCAAuC,EAAE;QACxEC,OAAO,EAAE;UACP,cAAc,EAAEH;QAClB,CAAC;QACDI,eAAe,EAAE;MACnB,CAAC,CAAC;MACFZ,OAAO,CAACS,QAAQ,CAACI,IAAI,CAACd,IAAI,CAAC;MAC3BK,YAAY,CAACI,eAAe,CAAC;IAC/B,CAAC,CAAC,OAAOM,KAAK,EAAE;MACdC,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;MACrChB,OAAO,CAAC,IAAI,CAAC;MACbI,YAAY,CAAC,IAAI,CAAC;MAClBC,YAAY,CAACY,UAAU,CAAC,WAAW,CAAC;IACtC,CAAC,SAAS;MACRf,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMgB,KAAK,GAAG,MAAAA,CAAOC,KAAK,EAAEC,QAAQ,KAAK;IACvC,IAAI;MACF,MAAMX,QAAQ,GAAG,MAAMrB,KAAK,CAACiC,IAAI,CAAC,sCAAsC,EAAE;QACxEF,KAAK;QACLC;MACF,CAAC,EAAE;QACDR,eAAe,EAAE;MACnB,CAAC,CAAC;;MAEF;MACA,MAAM;QAAEb,IAAI;QAAEI;MAAU,CAAC,GAAGM,QAAQ,CAACI,IAAI;MACzCb,OAAO,CAACD,IAAI,CAAC;MACbK,YAAY,CAACD,SAAS,CAAC;MACvBE,YAAY,CAACiB,OAAO,CAAC,WAAW,EAAEnB,SAAS,CAAC;MAE5C,OAAO;QACLoB,OAAO,EAAE,IAAI;QACbC,OAAO,EAAEf,QAAQ,CAACI,IAAI,CAACW,OAAO;QAC9BzB,IAAI,EAAEA;MACR,CAAC;IACH,CAAC,CAAC,OAAOe,KAAK,EAAE;MAAA,IAAAW,eAAA,EAAAC,oBAAA;MACd,OAAO;QACLH,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE,EAAAC,eAAA,GAAAX,KAAK,CAACL,QAAQ,cAAAgB,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBZ,IAAI,cAAAa,oBAAA,uBAApBA,oBAAA,CAAsBF,OAAO,KAAI;MAC5C,CAAC;IACH;EACF,CAAC;EAED,MAAMG,MAAM,GAAG,MAAOC,QAAQ,IAAK;IACjC,IAAI;MACFb,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEY,QAAQ,CAAC;MAC1D,MAAMnB,QAAQ,GAAG,MAAMrB,KAAK,CAACiC,IAAI,CAAC,uCAAuC,EAAEO,QAAQ,EAAE;QACnFhB,eAAe,EAAE;MACnB,CAAC,CAAC;MACFG,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEP,QAAQ,CAACI,IAAI,CAAC;MAC9C,OAAO;QAAEU,OAAO,EAAE,IAAI;QAAEC,OAAO,EAAEf,QAAQ,CAACI,IAAI,CAACW;MAAQ,CAAC;IAC1D,CAAC,CAAC,OAAOV,KAAK,EAAE;MAAA,IAAAe,gBAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACdhB,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrCC,OAAO,CAACD,KAAK,CAAC,iBAAiB,GAAAe,gBAAA,GAAEf,KAAK,CAACL,QAAQ,cAAAoB,gBAAA,uBAAdA,gBAAA,CAAgBhB,IAAI,CAAC;MACtD,OAAO;QACLU,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE,EAAAM,gBAAA,GAAAhB,KAAK,CAACL,QAAQ,cAAAqB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBjB,IAAI,cAAAkB,qBAAA,uBAApBA,qBAAA,CAAsBP,OAAO,KAAIV,KAAK,CAACU,OAAO,IAAI;MAC7D,CAAC;IACH;EACF,CAAC;EAED,MAAMQ,MAAM,GAAG,MAAAA,CAAA,KAAY;IACzB,IAAI;MACF,MAAMxB,eAAe,GAAGH,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;MACzD,IAAIE,eAAe,EAAE;QACnB,MAAMpB,KAAK,CAACiC,IAAI,CAAC,uCAAuC,EAAE,CAAC,CAAC,EAAE;UAC5DV,OAAO,EAAE;YACP,cAAc,EAAEH;UAClB,CAAC;UACDI,eAAe,EAAE;QACnB,CAAC,CAAC;MACJ;MAEAZ,OAAO,CAAC,IAAI,CAAC;MACbI,YAAY,CAAC,IAAI,CAAC;MAClBC,YAAY,CAACY,UAAU,CAAC,WAAW,CAAC;MACpCZ,YAAY,CAACY,UAAU,CAAC,MAAM,CAAC;MAC/BgB,cAAc,CAAChB,UAAU,CAAC,MAAM,CAAC;MACjC,OAAO;QAAEM,OAAO,EAAE,IAAI;QAAEC,OAAO,EAAE;MAA0B,CAAC;IAC9D,CAAC,CAAC,OAAOV,KAAK,EAAE;MACd;MACAd,OAAO,CAAC,IAAI,CAAC;MACbI,YAAY,CAAC,IAAI,CAAC;MAClBC,YAAY,CAACY,UAAU,CAAC,WAAW,CAAC;MACpCZ,YAAY,CAACY,UAAU,CAAC,MAAM,CAAC;MAC/BgB,cAAc,CAAChB,UAAU,CAAC,MAAM,CAAC;MACjC,OAAO;QACLM,OAAO,EAAE,IAAI;QAAE;QACfC,OAAO,EAAE;MACX,CAAC;IACH;EACF,CAAC;;EAED;EACArC,SAAS,CAAC,MAAM;IACd,MAAM+C,WAAW,GAAG9C,KAAK,CAAC+C,YAAY,CAACC,OAAO,CAACC,GAAG,CAAEC,MAAM,IAAK;MAC7D,MAAM9B,eAAe,GAAGH,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;MACzD,IAAIE,eAAe,EAAE;QACnB8B,MAAM,CAAC3B,OAAO,CAAC,cAAc,CAAC,GAAGH,eAAe;MAClD;MACA,OAAO8B,MAAM;IACf,CAAC,CAAC;IAEF,OAAO,MAAM;MACXlD,KAAK,CAAC+C,YAAY,CAACC,OAAO,CAACG,KAAK,CAACL,WAAW,CAAC;IAC/C,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMM,KAAK,GAAG;IACZzC,IAAI;IACJmB,KAAK;IACLS,MAAM;IACNK,MAAM;IACN/B,OAAO;IACPM,eAAe;IACfJ;EACF,CAAC;EAED,oBACEb,OAAA,CAACC,WAAW,CAACkD,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAA3C,QAAA,EAChCA;EAAQ;IAAA6C,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAAC/C,GAAA,CAjJWF,YAAY;AAAAkD,EAAA,GAAZlD,YAAY;AAAA,IAAAkD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}