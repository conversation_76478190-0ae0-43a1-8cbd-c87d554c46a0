import React, { createContext, useContext, useState, useEffect } from 'react';
import axios from 'axios';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  // Check if user is authenticated on app load
  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      const response = await axios.get('http://localhost:5000/api/auth/verify', {
        withCredentials: true
      });
      setUser(response.data.user);
    } catch (error) {
      console.log('User not authenticated');
      setUser(null);
    } finally {
      setLoading(false);
    }
  };

  const login = async (email, password) => {
    try {
      const response = await axios.post('http://localhost:5000/api/auth/login', {
        email,
        password
      }, {
        withCredentials: true
      });

      // Set user directly from login response
      setUser(response.data.user);
      return {
        success: true,
        message: response.data.message,
        user: response.data.user
      };
    } catch (error) {
      return {
        success: false,
        message: error.response?.data?.message || 'Login failed'
      };
    }
  };

  const signup = async (userData) => {
    try {
      console.log('Sending signup request with data:', userData);
      const response = await axios.post('http://localhost:5000/api/auth/signup', userData, {
        withCredentials: true
      });
      console.log('Signup response:', response.data);
      return { success: true, message: response.data.message };
    } catch (error) {
      console.error('Signup error:', error);
      console.error('Error response:', error.response?.data);
      return {
        success: false,
        message: error.response?.data?.message || error.message || 'Signup failed'
      };
    }
  };

  const logout = async () => {
    try {
      await axios.post('http://localhost:5000/api/auth/logout', {}, {
        withCredentials: true
      });
      setUser(null);
      // Clear any stored authentication data
      localStorage.removeItem('user');
      sessionStorage.removeItem('user');
      return { success: true, message: 'Logged out successfully' };
    } catch (error) {
      // Even if logout request fails, clear local state
      setUser(null);
      localStorage.removeItem('user');
      sessionStorage.removeItem('user');
      return {
        success: true, // Return success to ensure UI updates
        message: 'Logged out successfully'
      };
    }
  };

  const value = {
    user,
    login,
    signup,
    logout,
    loading,
    checkAuthStatus
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
