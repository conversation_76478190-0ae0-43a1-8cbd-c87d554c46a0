import React, { createContext, useContext, useState, useEffect } from 'react';
import axios from 'axios';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [sessionId, setSessionId] = useState(localStorage.getItem('sessionId'));

  // Check if user is authenticated on app load
  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      const storedSessionId = localStorage.getItem('sessionId');
      if (!storedSessionId) {
        setUser(null);
        setLoading(false);
        return;
      }

      const response = await axios.get('http://localhost:5000/api/auth/verify', {
        headers: {
          'x-session-id': storedSessionId
        },
        withCredentials: true
      });
      setUser(response.data.user);
      setSessionId(storedSessionId);
    } catch (error) {
      console.log('User not authenticated');
      setUser(null);
      setSessionId(null);
      localStorage.removeItem('sessionId');
    } finally {
      setLoading(false);
    }
  };

  const login = async (email, password) => {
    try {
      const response = await axios.post('http://localhost:5000/api/auth/login', {
        email,
        password
      }, {
        withCredentials: true
      });

      // Set user and session from login response
      const { user, sessionId } = response.data;
      setUser(user);
      setSessionId(sessionId);
      localStorage.setItem('sessionId', sessionId);

      return {
        success: true,
        message: response.data.message,
        user: user
      };
    } catch (error) {
      return {
        success: false,
        message: error.response?.data?.message || 'Login failed'
      };
    }
  };

  const signup = async (userData) => {
    try {
      console.log('Sending signup request with data:', userData);
      const response = await axios.post('http://localhost:5000/api/auth/signup', userData, {
        withCredentials: true
      });
      console.log('Signup response:', response.data);
      return { success: true, message: response.data.message };
    } catch (error) {
      console.error('Signup error:', error);
      console.error('Error response:', error.response?.data);
      return {
        success: false,
        message: error.response?.data?.message || error.message || 'Signup failed'
      };
    }
  };

  const logout = async () => {
    try {
      const storedSessionId = localStorage.getItem('sessionId');
      if (storedSessionId) {
        await axios.post('http://localhost:5000/api/auth/logout', {}, {
          headers: {
            'x-session-id': storedSessionId
          },
          withCredentials: true
        });
      }

      setUser(null);
      setSessionId(null);
      localStorage.removeItem('sessionId');
      localStorage.removeItem('user');
      sessionStorage.removeItem('user');
      return { success: true, message: 'Logged out successfully' };
    } catch (error) {
      // Even if logout request fails, clear local state
      setUser(null);
      setSessionId(null);
      localStorage.removeItem('sessionId');
      localStorage.removeItem('user');
      sessionStorage.removeItem('user');
      return {
        success: true, // Return success to ensure UI updates
        message: 'Logged out successfully'
      };
    }
  };

  // Set up axios interceptor to automatically add session header
  useEffect(() => {
    const interceptor = axios.interceptors.request.use((config) => {
      const storedSessionId = localStorage.getItem('sessionId');
      if (storedSessionId) {
        config.headers['x-session-id'] = storedSessionId;
      }
      return config;
    });

    return () => {
      axios.interceptors.request.eject(interceptor);
    };
  }, []);

  const value = {
    user,
    login,
    signup,
    logout,
    loading,
    checkAuthStatus,
    sessionId
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
