import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useAuth } from '../context/AuthContext';
import './AppointmentsTable.css';

const UserAppointmentHistory = () => {
  const [appointments, setAppointments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedReport, setSelectedReport] = useState(null);
  const { user } = useAuth();

  useEffect(() => {
    if (user) {
      fetchUserAppointments();
    }
  }, [user]);

  const fetchUserAppointments = async () => {
    try {
      const response = await axios.get(`http://localhost:5000/api/appointments/user/${user.email}`, {
        withCredentials: true
      });
      setAppointments(response.data.appointments);
    } catch (error) {
      console.error('Error fetching user appointments:', error);
      alert('Error fetching your appointments');
    } finally {
      setLoading(false);
    }
  };

  const viewReport = async (appointmentId) => {
    try {
      console.log(`📄 Fetching report for appointment: ${appointmentId}`);
      const response = await axios.get(`http://localhost:5000/api/patient-reports/appointment/${appointmentId}`);
      console.log(`✅ Report fetched successfully:`, response.data.report);
      setSelectedReport(response.data.report);
    } catch (error) {
      console.error('❌ Error fetching patient report:', error);
      if (error.response?.status === 404) {
        alert('No patient report available for this appointment yet.');
      } else if (error.response?.status === 401) {
        alert('Authentication required. Please log in again.');
      } else {
        alert('Error fetching patient report: ' + (error.response?.data?.message || error.message));
      }
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending': return '#BB86FC';
      case 'completed': return '#4CAF50';
      case 'cancelled': return '#f44336';
      case 'rescheduled': return '#FF9800';
      default: return '#888';
    }
  };

  if (loading) {
    return <div style={{ color: 'white', textAlign: 'center', padding: '2rem' }}>Loading your appointments...</div>;
  }

  return (
    <div style={{ padding: '2rem', color: 'white' }}>
      <h1 style={{ textAlign: 'center', marginBottom: '2rem' }}>My Appointment History</h1>
      
      {appointments.length === 0 ? (
        <p style={{ textAlign: 'center' }}>You have no appointments yet.</p>
      ) : (
        <div style={{ overflowX: 'auto' }}>
          <table className='appotable'>
            <thead>
              <tr>
                <th>Appointment Date</th>
                <th>Time Slot</th>
                <th>Doctor</th>
                <th>Department</th>
                <th>Status</th>
                <th>Visited</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {appointments.map((appointment) => (
                <tr key={appointment._id}>
                  <td>{formatDate(appointment.appointmentDate)}</td>
                  <td>{appointment.timeSlot}</td>
                  <td>
                    {appointment.doctor ? 
                      `Dr. ${appointment.doctor.firstName} ${appointment.doctor.lastName}` : 
                      'N/A'
                    }
                  </td>
                  <td>
                    {appointment.department ? 
                      appointment.department.name : 
                      'N/A'
                    }
                  </td>
                  <td>
                    <span style={{ 
                      color: getStatusColor(appointment.status),
                      fontWeight: 'bold',
                      textTransform: 'capitalize'
                    }}>
                      {appointment.status}
                    </span>
                    {appointment.rescheduled && (
                      <div style={{ fontSize: '0.8rem', color: '#FF9800' }}>
                        Rescheduled
                      </div>
                    )}
                  </td>
                  <td>
                    <span style={{ color: appointment.visited ? '#4CAF50' : '#f44336' }}>
                      {appointment.visited ? 'Yes' : 'No'}
                    </span>
                  </td>
                  <td>
                    <button 
                      onClick={() => viewReport(appointment._id)}
                      style={{
                        backgroundColor: '#007BFF',
                        color: 'white',
                        border: 'none',
                        padding: '0.5rem 1rem',
                        borderRadius: '4px',
                        cursor: 'pointer'
                      }}
                    >
                      View Report
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* Patient Report Modal */}
      {selectedReport && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0,0,0,0.8)',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          zIndex: 1000
        }}>
          <div style={{
            backgroundColor: '#1e1e1e',
            padding: '2rem',
            borderRadius: '8px',
            maxWidth: '600px',
            maxHeight: '80vh',
            overflow: 'auto',
            color: 'white'
          }}>
            <h2 style={{ color: '#BB86FC', marginBottom: '1rem' }}>Patient Report</h2>
            
            <div style={{ marginBottom: '1rem' }}>
              <strong>Status:</strong> {selectedReport.status}
            </div>
            
            <div style={{ marginBottom: '1rem' }}>
              <strong>Disease Details:</strong> {selectedReport.diseaseDetails}
            </div>
            
            {selectedReport.importantMeasures && selectedReport.importantMeasures.length > 0 && (
              <div style={{ marginBottom: '1rem' }}>
                <strong>Important Measures:</strong>
                <ul>
                  {selectedReport.importantMeasures.map((measure, index) => (
                    <li key={index}>{measure.label}: {measure.value}</li>
                  ))}
                </ul>
              </div>
            )}
            
            {selectedReport.allergies && selectedReport.allergies.length > 0 && (
              <div style={{ marginBottom: '1rem' }}>
                <strong>Allergies:</strong> {selectedReport.allergies.join(', ')}
              </div>
            )}
            
            {selectedReport.medications && (
              <div style={{ marginBottom: '1rem' }}>
                <strong>Medications:</strong> {selectedReport.medications}
              </div>
            )}
            
            {selectedReport.followUpPlan && (
              <div style={{ marginBottom: '1rem' }}>
                <strong>Follow-up Plan:</strong> {selectedReport.followUpPlan}
              </div>
            )}
            
            {selectedReport.labTests && (
              <div style={{ marginBottom: '1rem' }}>
                <strong>Lab Tests:</strong> {selectedReport.labTests}
              </div>
            )}
            
            {selectedReport.dietaryAdvice && (
              <div style={{ marginBottom: '1rem' }}>
                <strong>Dietary Advice:</strong> {selectedReport.dietaryAdvice}
              </div>
            )}
            
            {selectedReport.notes && (
              <div style={{ marginBottom: '1rem' }}>
                <strong>Doctor's Notes:</strong> {selectedReport.notes}
              </div>
            )}
            
            <button 
              onClick={() => setSelectedReport(null)}
              style={{
                backgroundColor: '#dc3545',
                color: 'white',
                border: 'none',
                padding: '0.5rem 1rem',
                borderRadius: '4px',
                cursor: 'pointer',
                marginTop: '1rem'
              }}
            >
              Close
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default UserAppointmentHistory;
