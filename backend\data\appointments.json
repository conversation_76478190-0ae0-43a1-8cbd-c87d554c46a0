[{"_id": 1, "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "email": "<EMAIL>", "mobileNumber": "1234567890", "nic": "123456789V", "gender": "male", "address": "123 Main St", "dob": "1990-01-15", "department": {"_id": "dept1", "name": "Cardiology", "description": "Heart and cardiovascular system"}, "doctor": {"_id": "doc1", "firstName": "<PERSON>", "lastName": "<PERSON>", "specialization": "Cardiology", "department": "dept1", "email": "<EMAIL>"}, "appointmentDate": "2025-06-27T13:44:23.525Z", "timeSlot": "9:00 AM - 10:00 AM", "status": "pending", "visited": false, "createdAt": "2025-06-26T13:44:23.527Z"}, {"_id": 2, "firstName": "<PERSON>", "lastName": "<PERSON>", "email": "<EMAIL>", "mobileNumber": "0987654321", "nic": "987654321V", "gender": "female", "address": "456 Oak Ave", "dob": "1985-05-20", "department": {"_id": "dept2", "name": "Neurology", "description": "Brain and nervous system"}, "doctor": {"_id": "doc2", "firstName": "<PERSON>", "lastName": "<PERSON>", "specialization": "Neurology", "department": "dept2", "email": "<EMAIL>"}, "appointmentDate": "2025-06-28T13:44:23.527Z", "timeSlot": "10:00 AM - 11:00 AM", "status": "pending", "visited": false, "createdAt": "2025-06-26T13:44:23.527Z"}, {"_id": 3, "firstName": "<PERSON>", "lastName": "<PERSON>", "email": "<EMAIL>", "mobileNumber": "9783552910", "nic": "NIC8910", "gender": "male", "address": "homw123", "dob": "1950-11-02", "department": {"_id": "dept1", "name": "Cardiology", "description": "Heart and cardiovascular system"}, "doctor": {"_id": "doc5", "firstName": "smith", "lastName": "", "email": "<EMAIL>", "specialization": "General Medicine", "department": "dept1"}, "appointmentDate": "2025-06-27", "timeSlot": "9:00 AM - 10:00 AM", "status": "pending", "visited": false, "createdAt": "2025-06-26T13:45:48.649Z"}]